"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { api } from "../../utils/api"
import SongCard from "../../components/music/SongCard"
import AlbumCard from "../../components/music/AlbumCard"

export default function ArtistPage() {
  const router = useRouter()
  const { id } = router.query
  const [artist, setArtist] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("songs")

  useEffect(() => {
    if (id) {
      fetchArtist()
    }
  }, [id])

  const fetchArtist = async () => {
    try {
      const response = await api.getArtist(id)
      if (response.success) {
        setArtist(response.data)
      }
    } catch (error) {
      console.error("Error fetching artist:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (!artist) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Artist not found</p>
      </div>
    )
  }

  const artistImage = artist.image?.find((img) => img.quality === "500x500") || artist.image?.[0]

  return (
    <div className="p-6">
      {/* Artist Header */}
      <div className="flex flex-col md:flex-row items-start md:items-end space-y-4 md:space-y-0 md:space-x-6 mb-8">
        {artistImage && (
          <img
            src={artistImage.url || "/placeholder.svg"}
            alt={artist.name}
            className="w-48 h-48 rounded-full object-cover shadow-2xl"
          />
        )}
        <div className="flex-1">
          <p className="text-sm text-gray-400 mb-2">Artist</p>
          <h1 className="text-4xl md:text-6xl font-bold mb-4">{artist.name}</h1>
          {artist.followerCount && <p className="text-gray-400">{artist.followerCount.toLocaleString()} followers</p>}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700 mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab("songs")}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm transition-colors
              ${
                activeTab === "songs"
                  ? "border-green-500 text-green-400"
                  : "border-transparent text-gray-400 hover:text-gray-300"
              }
            `}
          >
            Popular Songs
          </button>
          <button
            onClick={() => setActiveTab("albums")}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm transition-colors
              ${
                activeTab === "albums"
                  ? "border-green-500 text-green-400"
                  : "border-transparent text-gray-400 hover:text-gray-300"
              }
            `}
          >
            Albums
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "songs" && artist.topSongs && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {artist.topSongs.map((song) => (
            <SongCard key={song.id} song={song} songs={artist.topSongs} />
          ))}
        </div>
      )}

      {activeTab === "albums" && artist.topAlbums && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {artist.topAlbums.map((album) => (
            <AlbumCard key={album.id} album={album} />
          ))}
        </div>
      )}
    </div>
  )
}
