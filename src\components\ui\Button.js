"use client"

import { forwardRef } from "react"

const Button = forwardRef(({ 
  children, 
  variant = "primary", 
  size = "md", 
  disabled = false, 
  loading = false,
  className = "",
  ...props 
}, ref) => {
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
  
  const variants = {
    primary: "bg-gradient-to-r from-accent-primary to-accent-secondary text-white hover:from-accent-secondary hover:to-accent-tertiary shadow-lg hover:shadow-glow-md",
    secondary: "bg-background-card text-text-primary border border-border-primary hover:bg-background-hover hover:border-border-secondary",
    ghost: "text-text-secondary hover:text-text-primary hover:bg-background-hover",
    danger: "bg-red-600 text-white hover:bg-red-700 shadow-lg",
    success: "bg-green-600 text-white hover:bg-green-700 shadow-lg"
  }
  
  const sizes = {
    sm: "px-3 py-1.5 text-sm rounded-md",
    md: "px-4 py-2 text-sm rounded-lg",
    lg: "px-6 py-3 text-base rounded-xl",
    xl: "px-8 py-4 text-lg rounded-2xl",
    icon: "p-2 rounded-lg"
  }
  
  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`
  
  return (
    <button
      ref={ref}
      className={classes}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="loading-dots">
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
          </div>
        </div>
      )}
      <span className={loading ? "opacity-0" : "opacity-100"}>
        {children}
      </span>
      {variant === "primary" && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></div>
      )}
    </button>
  )
})

Button.displayName = "Button"

export default Button
