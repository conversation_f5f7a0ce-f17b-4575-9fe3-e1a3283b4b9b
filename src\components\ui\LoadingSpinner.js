"use client"

export default function LoadingSpinner({ size = "md", variant = "primary", className = "" }) {
  const sizes = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8",
    xl: "w-12 h-12"
  }
  
  const variants = {
    primary: "text-accent-primary",
    secondary: "text-text-secondary",
    white: "text-white"
  }
  
  return (
    <div className={`animate-spin ${sizes[size]} ${variants[variant]} ${className}`}>
      <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
        <circle 
          className="opacity-25" 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          strokeWidth="4"
        />
        <path 
          className="opacity-75" 
          fill="currentColor" 
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  )
}

export function LoadingDots({ className = "" }) {
  return (
    <div className={`loading-dots ${className}`}>
      <div className="loading-dot"></div>
      <div className="loading-dot"></div>
      <div className="loading-dot"></div>
    </div>
  )
}

export function MusicBars({ className = "", playing = true }) {
  return (
    <div className={`music-bars ${className}`}>
      <div className={`music-bar ${!playing ? 'opacity-50' : ''}`}></div>
      <div className={`music-bar ${!playing ? 'opacity-50' : ''}`}></div>
      <div className={`music-bar ${!playing ? 'opacity-50' : ''}`}></div>
      <div className={`music-bar ${!playing ? 'opacity-50' : ''}`}></div>
      <div className={`music-bar ${!playing ? 'opacity-50' : ''}`}></div>
    </div>
  )
}

export function Skeleton({ className = "", width, height }) {
  const style = {}
  if (width) style.width = width
  if (height) style.height = height
  
  return (
    <div 
      className={`skeleton ${className}`}
      style={style}
    />
  )
}
