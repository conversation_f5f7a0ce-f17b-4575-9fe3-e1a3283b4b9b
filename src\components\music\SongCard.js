"use client";

import { useState } from "react";
import { PlayIcon, PauseIcon, EllipsisVerticalIcon, PlusIcon, HeartIcon } from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";
import { usePlayer } from "../layout/Layout";
import { usePlaylist } from "../layout/Layout";
import { useFavorites } from "../../hooks/useFavorites";
import Button from "../ui/Button";
import Card from "../ui/Card";
import { MusicBars, LoadingSpinner } from "../ui/LoadingSpinner";

export default function SongCard({ song, songs = [], showAddToPlaylist = true }) {
    const [showMenu, setShowMenu] = useState(false);
    const { currentSong, isPlaying, isLoading, playSong, togglePlayPause } = usePlayer();
    const { playlists, addToPlaylist } = usePlaylist();
    const { isFavorite, toggleFavorite } = useFavorites();

    const isCurrentSong = currentSong?.id === song.id;
    const songImage = song.image?.find((img) => img.quality === "150x150") || song.image?.[0];
    const artistName = song.artists?.primary?.[0]?.name || "Unknown Artist";

    const handlePlay = () => {
        if (isCurrentSong) {
            togglePlayPause();
        } else {
            playSong(song, songs);
        }
    };

    const handleAddToPlaylist = (playlistId) => {
        addToPlaylist(playlistId, song);
        setShowMenu(false);
    };

    const handleFavoriteToggle = (e) => {
        e.stopPropagation();
        toggleFavorite(song);
    };

    return (
        <Card className="group relative p-4 card-hover bg-background-card border-border-primary">
            <div className="relative">
                {songImage && (
                    <div className="relative overflow-hidden rounded-xl mb-4">
                        <img
                            src={songImage.url || "/placeholder.svg"}
                            alt={song.name}
                            className="w-full aspect-square object-cover transition-transform duration-300 group-hover:scale-110"
                        />

                        {/* Gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                        {/* Music bars overlay when playing */}
                        {isCurrentSong && isPlaying && (
                            <div className="absolute inset-0 flex items-center justify-center bg-black/40">
                                <MusicBars className="w-8 h-8" playing={isPlaying} />
                            </div>
                        )}
                    </div>
                )}

                {/* Play Button Overlay */}
                <Button
                    variant="primary"
                    size="icon"
                    onClick={handlePlay}
                    disabled={isLoading && isCurrentSong}
                    className={`
                        absolute bottom-6 right-6 w-12 h-12 shadow-glow-md transition-all duration-300
                        ${
                            isCurrentSong && isPlaying
                                ? "opacity-100 scale-100"
                                : "opacity-0 group-hover:opacity-100 group-hover:scale-100 scale-90"
                        }
                    `}
                >
                    {isLoading && isCurrentSong ? (
                        <LoadingSpinner size="sm" variant="white" />
                    ) : isCurrentSong && isPlaying ? (
                        <PauseIcon className="w-5 h-5" />
                    ) : (
                        <PlayIcon className="w-5 h-5 ml-0.5" />
                    )}
                </Button>

                {/* Favorite Button */}
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleFavoriteToggle}
                    className={`
                        absolute top-3 right-3 w-8 h-8 transition-all duration-300
                        ${
                            isFavorite(song.id)
                                ? "opacity-100 text-accent-primary"
                                : "opacity-0 group-hover:opacity-100 text-text-tertiary hover:text-accent-primary"
                        }
                    `}
                >
                    {isFavorite(song.id) ? (
                        <HeartIcon className="w-4 h-4" />
                    ) : (
                        <HeartOutlineIcon className="w-4 h-4" />
                    )}
                </Button>
            </div>

            <div className="flex items-start justify-between">
                <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-text-primary truncate mb-1 group-hover:text-accent-primary transition-colors">
                        {song.name}
                    </h3>
                    <p className="text-sm text-text-secondary truncate hover:text-text-primary transition-colors cursor-pointer">
                        {artistName}
                    </p>
                    {song.album?.name && (
                        <p className="text-xs text-text-tertiary truncate mt-1 hover:text-text-secondary transition-colors cursor-pointer">
                            {song.album.name}
                        </p>
                    )}
                </div>

                {showAddToPlaylist && (
                    <div className="relative">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setShowMenu(!showMenu)}
                            className="opacity-0 group-hover:opacity-100 text-text-tertiary hover:text-text-primary"
                        >
                            <EllipsisVerticalIcon className="w-5 h-5" />
                        </Button>

                        {showMenu && (
                            <div className="absolute right-0 top-8 glass-dark rounded-xl shadow-2xl py-2 z-20 min-w-48 border border-border-secondary animate-scale-in">
                                <div className="px-4 py-2 text-xs text-text-tertiary border-b border-border-secondary mb-1">
                                    Add to Playlist
                                </div>
                                {playlists.map((playlist) => (
                                    <Button
                                        key={playlist.id}
                                        variant="ghost"
                                        onClick={() => handleAddToPlaylist(playlist.id)}
                                        className="w-full justify-start px-4 py-2 text-sm text-text-primary hover:bg-background-hover rounded-none"
                                    >
                                        <PlusIcon className="w-4 h-4 mr-3 text-accent-primary" />
                                        {playlist.name}
                                    </Button>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Click outside to close menu */}
            {showMenu && <div className="fixed inset-0 z-10" onClick={() => setShowMenu(false)} />}
        </div>
    );
}
