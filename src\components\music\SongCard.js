"use client"

import { useState } from "react"
import { PlayIcon, PauseIcon, EllipsisVerticalIcon, PlusIcon } from "@heroicons/react/24/solid"
import { usePlayer } from "../layout/Layout"
import { usePlaylist } from "../layout/Layout"

export default function SongCard({ song, songs = [], showAddToPlaylist = true }) {
  const [showMenu, setShowMenu] = useState(false)
  const { currentSong, isPlaying, playSong, togglePlayPause } = usePlayer()
  const { playlists, addToPlaylist } = usePlaylist()

  const isCurrentSong = currentSong?.id === song.id
  const songImage = song.image?.find((img) => img.quality === "150x150") || song.image?.[0]

  const handlePlay = () => {
    if (isCurrentSong) {
      togglePlayPause()
    } else {
      playSong(song, songs)
    }
  }

  const handleAddToPlaylist = (playlistId) => {
    addToPlaylist(playlistId, song)
    setShowMenu(false)
  }

  return (
    <div className="group relative bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-all duration-200 transform hover:scale-105">
      <div className="relative">
        {songImage && (
          <img
            src={songImage.url || "/placeholder.svg"}
            alt={song.name}
            className="w-full aspect-square object-cover rounded-md mb-4"
          />
        )}

        {/* Play Button Overlay */}
        <button
          onClick={handlePlay}
          className={`
            absolute bottom-2 right-2 p-3 rounded-full shadow-lg transition-all duration-200
            ${
              isCurrentSong && isPlaying
                ? "bg-green-500 opacity-100"
                : "bg-green-500 opacity-0 group-hover:opacity-100 hover:scale-110"
            }
          `}
        >
          {isCurrentSong && isPlaying ? (
            <PauseIcon className="w-5 h-5 text-white" />
          ) : (
            <PlayIcon className="w-5 h-5 text-white" />
          )}
        </button>
      </div>

      <div className="flex items-start justify-between">
        <div className="min-w-0 flex-1">
          <h3 className="font-medium text-white truncate mb-1">{song.name}</h3>
          <p className="text-sm text-gray-400 truncate">{song.artists?.primary?.[0]?.name}</p>
          {song.album?.name && <p className="text-xs text-gray-500 truncate mt-1">{song.album.name}</p>}
        </div>

        {showAddToPlaylist && (
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1 rounded-full hover:bg-gray-600 transition-colors opacity-0 group-hover:opacity-100"
            >
              <EllipsisVerticalIcon className="w-5 h-5 text-gray-400" />
            </button>

            {showMenu && (
              <div className="absolute right-0 top-8 bg-gray-700 rounded-lg shadow-lg py-2 z-10 min-w-48">
                <div className="px-3 py-1 text-xs text-gray-400 border-b border-gray-600 mb-1">Add to Playlist</div>
                {playlists.map((playlist) => (
                  <button
                    key={playlist.id}
                    onClick={() => handleAddToPlaylist(playlist.id)}
                    className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-600 flex items-center"
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    {playlist.name}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Click outside to close menu */}
      {showMenu && <div className="fixed inset-0 z-5" onClick={() => setShowMenu(false)} />}
    </div>
  )
}
