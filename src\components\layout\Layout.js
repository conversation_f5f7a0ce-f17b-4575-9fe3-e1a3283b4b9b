"use client"

import { useState, createContext, useContext } from "react"
import Sidebar from "./Sidebar"
import Header from "./Header"
import AudioPlayer from "../player/AudioPlayer"
import { useAudioPlayer } from "../../hooks/useAudioPlayer"
import { useLocalStorage } from "../../hooks/useLocalStorage"

const PlayerContext = createContext()
const PlaylistContext = createContext()

export const usePlayer = () => useContext(PlayerContext)
export const usePlaylist = () => useContext(PlaylistContext)

export default function Layout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const playerState = useAudioPlayer()
  const [playlists, setPlaylists] = useLocalStorage("user-playlists", [])

  const addToPlaylist = (playlistId, song) => {
    setPlaylists((prev) =>
      prev.map((playlist) =>
        playlist.id === playlistId ? { ...playlist, songs: [...playlist.songs, song] } : playlist,
      ),
    )
  }

  const createPlaylist = (name) => {
    const newPlaylist = {
      id: Date.now().toString(),
      name,
      songs: [],
      createdAt: new Date().toISOString(),
    }
    setPlaylists((prev) => [...prev, newPlaylist])
    return newPlaylist
  }

  const deletePlaylist = (playlistId) => {
    setPlaylists((prev) => prev.filter((p) => p.id !== playlistId))
  }

  return (
    <PlayerContext.Provider value={playerState}>
      <PlaylistContext.Provider
        value={{
          playlists,
          addToPlaylist,
          createPlaylist,
          deletePlaylist,
        }}
      >
        <div className="min-h-screen bg-gray-900 text-white">
          <div className="flex">
            <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

            <div className="flex-1 flex flex-col">
              <Header onMenuClick={() => setSidebarOpen(true)} />

              <main className="flex-1 overflow-y-auto pb-24">{children}</main>
            </div>
          </div>

          <AudioPlayer />
        </div>
      </PlaylistContext.Provider>
    </PlayerContext.Provider>
  )
}
