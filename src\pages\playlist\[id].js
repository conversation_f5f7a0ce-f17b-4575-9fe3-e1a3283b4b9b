"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { PlayIcon, TrashIcon } from "@heroicons/react/24/solid"
import { MusicalNoteIcon } from "@heroicons/react/24/outline"
import { usePlaylist } from "../../components/layout/Layout"
import { usePlayer } from "../../components/layout/Layout"

export default function PlaylistPage() {
  const router = useRouter()
  const { id } = router.query
  const { playlists, deletePlaylist } = usePlaylist()
  const { playSong, currentSong, isPlaying } = usePlayer()
  const [playlist, setPlaylist] = useState(null)

  useEffect(() => {
    if (id && playlists.length > 0) {
      const foundPlaylist = playlists.find((p) => p.id === id)
      setPlaylist(foundPlaylist)
    }
  }, [id, playlists])

  const handlePlayPlaylist = () => {
    if (playlist && playlist.songs.length > 0) {
      playSong(playlist.songs[0], playlist.songs)
    }
  }

  const handlePlaySong = (song) => {
    playSong(song, playlist.songs)
  }

  const handleDeletePlaylist = () => {
    if (confirm("Are you sure you want to delete this playlist?")) {
      deletePlaylist(id)
      router.push("/my-playlists")
    }
  }

  const removeSongFromPlaylist = (songIndex) => {
    // This would need to be implemented in the playlist context
    // For now, we'll just show an alert
    alert("Remove song functionality would be implemented here")
  }

  if (!playlist) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Playlist not found</p>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Playlist Header */}
      <div className="flex flex-col md:flex-row items-start md:items-end space-y-4 md:space-y-0 md:space-x-6 mb-8">
        <div className="w-48 h-48 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center shadow-2xl">
          <MusicalNoteIcon className="w-24 h-24 text-white" />
        </div>
        <div className="flex-1">
          <p className="text-sm text-gray-400 mb-2">Playlist</p>
          <h1 className="text-4xl md:text-6xl font-bold mb-4">{playlist.name}</h1>
          <div className="flex items-center space-x-2 text-gray-400">
            <span>
              {playlist.songs.length} song{playlist.songs.length !== 1 ? "s" : ""}
            </span>
            <span>•</span>
            <span>Created {new Date(playlist.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-4 mb-8">
        {playlist.songs.length > 0 && (
          <button
            onClick={handlePlayPlaylist}
            className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-medium transition-colors"
          >
            <PlayIcon className="w-5 h-5" />
            <span>Play</span>
          </button>
        )}
        <button
          onClick={handleDeletePlaylist}
          className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full font-medium transition-colors"
        >
          <TrashIcon className="w-5 h-5" />
          <span>Delete Playlist</span>
        </button>
      </div>

      {/* Songs List */}
      {playlist.songs.length === 0 ? (
        <div className="text-center py-12">
          <MusicalNoteIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold mb-2">No songs in this playlist</h2>
          <p className="text-gray-400">Add songs by searching and using the "Add to Playlist" option</p>
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-700">
            <h2 className="text-xl font-semibold">Songs</h2>
          </div>
          <div className="divide-y divide-gray-700">
            {playlist.songs.map((song, index) => {
              const isCurrentSong = currentSong?.id === song.id
              const songImage = song.image?.find((img) => img.quality === "50x50") || song.image?.[0]

              return (
                <div
                  key={`${song.id}-${index}`}
                  className={`
                    flex items-center space-x-4 px-6 py-4 hover:bg-gray-700 group transition-colors
                    ${isCurrentSong ? "bg-gray-700" : ""}
                  `}
                >
                  <div className="w-8 text-center">
                    {isCurrentSong && isPlaying ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 flex items-center justify-center">
                          <div className="flex space-x-1">
                            <div className="w-1 h-3 bg-green-500 animate-pulse"></div>
                            <div
                              className="w-1 h-2 bg-green-500 animate-pulse"
                              style={{ animationDelay: "0.1s" }}
                            ></div>
                            <div
                              className="w-1 h-4 bg-green-500 animate-pulse"
                              style={{ animationDelay: "0.2s" }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <button
                        onClick={() => handlePlaySong(song)}
                        className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-600 transition-colors"
                      >
                        <PlayIcon className="w-4 h-4 text-gray-400" />
                      </button>
                    )}
                  </div>

                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {songImage && (
                      <img
                        src={songImage.url || "/placeholder.svg"}
                        alt={song.name}
                        className="w-10 h-10 rounded object-cover"
                      />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className={`font-medium truncate ${isCurrentSong ? "text-green-400" : "text-white"}`}>
                        {song.name}
                      </p>
                      <p className="text-sm text-gray-400 truncate">{song.artists?.primary?.[0]?.name}</p>
                    </div>
                  </div>

                  <button
                    onClick={() => removeSongFromPlaylist(index)}
                    className="p-2 text-gray-400 hover:text-red-400 transition-colors opacity-0 group-hover:opacity-100"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
