"use client"

import { useState, useRef, useEffect } from "react"

export const useAudioPlayer = () => {
  const audioRef = useRef(null)
  const [currentSong, setCurrentSong] = useState(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [queue, setQueue] = useState([])
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const updateTime = () => setCurrentTime(audio.currentTime)
    const updateDuration = () => setDuration(audio.duration)
    const handleEnded = () => playNext()

    audio.addEventListener("timeupdate", updateTime)
    audio.addEventListener("loadedmetadata", updateDuration)
    audio.addEventListener("ended", handleEnded)

    return () => {
      audio.removeEventListener("timeupdate", updateTime)
      audio.removeEventListener("loadedmetadata", updateDuration)
      audio.removeEventListener("ended", handleEnded)
    }
  }, [currentSong])

  const playSong = (song, songQueue = []) => {
    if (songQueue.length > 0) {
      setQueue(songQueue)
      const index = songQueue.findIndex((s) => s.id === song.id)
      setCurrentIndex(index >= 0 ? index : 0)
    }

    setCurrentSong(song)
    if (audioRef.current) {
      const downloadUrl = song.downloadUrl?.find((url) => url.quality === "320kbps") || song.downloadUrl?.[0]
      if (downloadUrl) {
        audioRef.current.src = downloadUrl.url
        audioRef.current.play()
        setIsPlaying(true)
      }
    }
  }

  const togglePlayPause = () => {
    if (!audioRef.current || !currentSong) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      audioRef.current.play()
      setIsPlaying(true)
    }
  }

  const playNext = () => {
    if (queue.length === 0) return
    const nextIndex = (currentIndex + 1) % queue.length
    setCurrentIndex(nextIndex)
    playSong(queue[nextIndex], queue)
  }

  const playPrevious = () => {
    if (queue.length === 0) return
    const prevIndex = currentIndex === 0 ? queue.length - 1 : currentIndex - 1
    setCurrentIndex(prevIndex)
    playSong(queue[prevIndex], queue)
  }

  const seek = (time) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time
      setCurrentTime(time)
    }
  }

  const changeVolume = (newVolume) => {
    setVolume(newVolume)
    if (audioRef.current) {
      audioRef.current.volume = newVolume
    }
  }

  return {
    audioRef,
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    queue,
    currentIndex,
    playSong,
    togglePlayPause,
    playNext,
    playPrevious,
    seek,
    changeVolume,
  }
}
