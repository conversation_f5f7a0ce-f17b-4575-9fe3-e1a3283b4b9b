"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { useRecentlyPlayed } from "./useRecentlyPlayed";

export const useAudioPlayer = () => {
    const audioRef = useRef(null);
    const [currentSong, setCurrentSong] = useState(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);
    const [queue, setQueue] = useState([]);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [repeatMode, setRepeatMode] = useState("off"); // "off", "one", "all"
    const [shuffleMode, setShuffleMode] = useState(false);
    const [shuffledQueue, setShuffledQueue] = useState([]);
    const [originalQueue, setOriginalQueue] = useState([]);
    const [audioQuality, setAudioQuality] = useState("320kbps");
    const [crossfadeEnabled, setCrossfadeEnabled] = useState(false);
    const [crossfadeDuration, setCrossfadeDuration] = useState(3); // seconds

    const { addToRecentlyPlayed, incrementPlayCount } = useRecentlyPlayed();

    // Shuffle utility function
    const shuffleArray = useCallback((array) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }, []);

    // Get current queue based on shuffle mode
    const getCurrentQueue = useCallback(() => {
        return shuffleMode ? shuffledQueue : queue;
    }, [shuffleMode, shuffledQueue, queue]);

    // Get audio URL with quality preference
    const getAudioUrl = useCallback(
        (song) => {
            const qualityPriority = [audioQuality, "320kbps", "160kbps", "96kbps", "48kbps"];

            for (const quality of qualityPriority) {
                const url = song.downloadUrl?.find((url) => url.quality === quality);
                if (url) return url.url;
            }

            return song.downloadUrl?.[0]?.url;
        },
        [audioQuality]
    );

    // Handle song end based on repeat mode
    const handleSongEnd = useCallback(() => {
        if (repeatMode === "one") {
            // Repeat current song
            if (audioRef.current) {
                audioRef.current.currentTime = 0;
                audioRef.current.play();
                setIsPlaying(true);
            }
        } else {
            // Play next song
            playNext();
        }
    }, [repeatMode]);

    useEffect(() => {
        const audio = audioRef.current;
        if (!audio) return;

        const updateTime = () => setCurrentTime(audio.currentTime);
        const updateDuration = () => setDuration(audio.duration);
        const handleLoadStart = () => setIsLoading(true);
        const handleCanPlay = () => setIsLoading(false);
        const handleError = (e) => {
            setError("Failed to load audio");
            setIsLoading(false);
            console.error("Audio error:", e);
        };

        audio.addEventListener("timeupdate", updateTime);
        audio.addEventListener("loadedmetadata", updateDuration);
        audio.addEventListener("loadstart", handleLoadStart);
        audio.addEventListener("canplay", handleCanPlay);
        audio.addEventListener("error", handleError);
        audio.addEventListener("ended", handleSongEnd);

        return () => {
            audio.removeEventListener("timeupdate", updateTime);
            audio.removeEventListener("loadedmetadata", updateDuration);
            audio.removeEventListener("loadstart", handleLoadStart);
            audio.removeEventListener("canplay", handleCanPlay);
            audio.removeEventListener("error", handleError);
            audio.removeEventListener("ended", handleSongEnd);
        };
    }, [handleSongEnd]);

    const playSong = useCallback(
        async (song, songQueue = []) => {
            try {
                setError(null);
                setIsLoading(true);

                if (songQueue.length > 0) {
                    setQueue(songQueue);
                    setOriginalQueue(songQueue);
                    const index = songQueue.findIndex((s) => s.id === song.id);
                    setCurrentIndex(index >= 0 ? index : 0);

                    // Update shuffled queue if shuffle is enabled
                    if (shuffleMode) {
                        const shuffled = shuffleArray(songQueue);
                        setShuffledQueue(shuffled);
                    }
                }

                setCurrentSong(song);

                // Add to recently played
                await addToRecentlyPlayed(song);

                if (audioRef.current) {
                    const audioUrl = getAudioUrl(song);
                    if (audioUrl) {
                        audioRef.current.src = audioUrl;
                        audioRef.current.volume = volume;

                        try {
                            await audioRef.current.play();
                            setIsPlaying(true);
                        } catch (playError) {
                            console.error("Play error:", playError);
                            setError("Failed to play audio");
                        }
                    } else {
                        setError("No audio URL available");
                    }
                }
            } catch (error) {
                console.error("Error playing song:", error);
                setError("Failed to play song");
            } finally {
                setIsLoading(false);
            }
        },
        [volume, addToRecentlyPlayed, getAudioUrl, shuffleMode, shuffleArray]
    );

    const togglePlayPause = useCallback(async () => {
        if (!audioRef.current || !currentSong) return;

        try {
            if (isPlaying) {
                audioRef.current.pause();
                setIsPlaying(false);
            } else {
                await audioRef.current.play();
                setIsPlaying(true);
            }
        } catch (error) {
            console.error("Error toggling play/pause:", error);
            setError("Failed to play/pause audio");
        }
    }, [isPlaying, currentSong]);

    const playNext = useCallback(() => {
        const currentQueue = getCurrentQueue();
        if (currentQueue.length === 0) return;

        let nextIndex;
        if (repeatMode === "all") {
            nextIndex = (currentIndex + 1) % currentQueue.length;
        } else {
            nextIndex = currentIndex + 1;
            if (nextIndex >= currentQueue.length) {
                // End of queue, stop playing
                setIsPlaying(false);
                return;
            }
        }

        setCurrentIndex(nextIndex);
        playSong(currentQueue[nextIndex], originalQueue);
    }, [getCurrentQueue, currentIndex, repeatMode, playSong, originalQueue]);

    const playPrevious = useCallback(() => {
        const currentQueue = getCurrentQueue();
        if (currentQueue.length === 0) return;

        const prevIndex = currentIndex === 0 ? currentQueue.length - 1 : currentIndex - 1;
        setCurrentIndex(prevIndex);
        playSong(currentQueue[prevIndex], originalQueue);
    }, [getCurrentQueue, currentIndex, playSong, originalQueue]);

    const seek = useCallback((time) => {
        if (audioRef.current) {
            audioRef.current.currentTime = time;
            setCurrentTime(time);
        }
    }, []);

    const changeVolume = useCallback((newVolume) => {
        setVolume(newVolume);
        if (audioRef.current) {
            audioRef.current.volume = newVolume;
        }
    }, []);

    // Toggle shuffle mode
    const toggleShuffle = useCallback(() => {
        if (!shuffleMode) {
            // Enable shuffle
            const shuffled = shuffleArray(queue);
            setShuffledQueue(shuffled);
            setShuffleMode(true);
        } else {
            // Disable shuffle
            setShuffleMode(false);
            setShuffledQueue([]);
        }
    }, [shuffleMode, queue, shuffleArray]);

    // Toggle repeat mode
    const toggleRepeat = useCallback(() => {
        const modes = ["off", "all", "one"];
        const currentModeIndex = modes.indexOf(repeatMode);
        const nextMode = modes[(currentModeIndex + 1) % modes.length];
        setRepeatMode(nextMode);
    }, [repeatMode]);

    // Add song to queue
    const addToQueue = useCallback(
        (song) => {
            setQueue((prev) => [...prev, song]);
            setOriginalQueue((prev) => [...prev, song]);

            if (shuffleMode) {
                setShuffledQueue((prev) => [...prev, song]);
            }
        },
        [shuffleMode]
    );

    // Remove song from queue
    const removeFromQueue = useCallback(
        (index) => {
            setQueue((prev) => prev.filter((_, i) => i !== index));
            setOriginalQueue((prev) => prev.filter((_, i) => i !== index));

            if (shuffleMode) {
                setShuffledQueue((prev) => prev.filter((_, i) => i !== index));
            }

            // Adjust current index if necessary
            if (index < currentIndex) {
                setCurrentIndex((prev) => prev - 1);
            } else if (index === currentIndex && index >= queue.length - 1) {
                setCurrentIndex(0);
            }
        },
        [shuffleMode, currentIndex, queue.length]
    );

    // Clear queue
    const clearQueue = useCallback(() => {
        setQueue([]);
        setOriginalQueue([]);
        setShuffledQueue([]);
        setCurrentIndex(0);
    }, []);

    return {
        // Audio element ref
        audioRef,

        // Playback state
        currentSong,
        isPlaying,
        isLoading,
        error,
        currentTime,
        duration,
        volume,

        // Queue management
        queue: getCurrentQueue(),
        originalQueue,
        currentIndex,

        // Playback modes
        repeatMode,
        shuffleMode,
        audioQuality,
        crossfadeEnabled,
        crossfadeDuration,

        // Core playback functions
        playSong,
        togglePlayPause,
        playNext,
        playPrevious,
        seek,
        changeVolume,

        // Mode controls
        toggleShuffle,
        toggleRepeat,
        setAudioQuality,
        setCrossfadeEnabled,
        setCrossfadeDuration,

        // Queue management
        addToQueue,
        removeFromQueue,
        clearQueue,

        // Utility functions
        getCurrentQueue,
        getAudioUrl,

        // State helpers
        hasNext: currentIndex < getCurrentQueue().length - 1,
        hasPrevious: currentIndex > 0,
        queueLength: getCurrentQueue().length,
    };
};
