import Link from "next/link"

export default function ArtistCard({ artist }) {
  const artistImage = artist.image?.find((img) => img.quality === "500x500") || artist.image?.[0]

  return (
    <Link href={`/artist/${artist.id}`}>
      <div className="group bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-all duration-200 transform hover:scale-105 cursor-pointer">
        {artistImage && (
          <img
            src={artistImage.url || "/placeholder.svg"}
            alt={artist.name}
            className="w-full aspect-square object-cover rounded-full mb-4"
          />
        )}

        <h3 className="font-medium text-white text-center truncate">{artist.name}</h3>
        <p className="text-sm text-gray-400 text-center">Artist</p>
      </div>
    </Link>
  )
}
