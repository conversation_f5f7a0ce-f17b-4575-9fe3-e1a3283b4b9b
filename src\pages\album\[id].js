"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { PlayIcon } from "@heroicons/react/24/solid"
import { api } from "../../utils/api"
import { usePlayer } from "../../components/layout/Layout"

export default function AlbumPage() {
  const router = useRouter()
  const { id } = router.query
  const [album, setAlbum] = useState(null)
  const [loading, setLoading] = useState(true)
  const { playSong, currentSong, isPlaying } = usePlayer()

  useEffect(() => {
    if (id) {
      fetchAlbum()
    }
  }, [id])

  const fetchAlbum = async () => {
    try {
      const response = await api.getAlbum(id)
      if (response.success) {
        setAlbum(response.data)
      }
    } catch (error) {
      console.error("Error fetching album:", error)
    } finally {
      setLoading(false)
    }
  }

  const handlePlayAlbum = () => {
    if (album.songs && album.songs.length > 0) {
      playSong(album.songs[0], album.songs)
    }
  }

  const handlePlaySong = (song, index) => {
    playSong(song, album.songs)
  }

  const formatDuration = (duration) => {
    if (!duration) return ""
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (!album) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Album not found</p>
      </div>
    )
  }

  const albumImage = album.image?.find((img) => img.quality === "500x500") || album.image?.[0]

  return (
    <div className="p-6">
      {/* Album Header */}
      <div className="flex flex-col md:flex-row items-start md:items-end space-y-4 md:space-y-0 md:space-x-6 mb-8">
        {albumImage && (
          <img
            src={albumImage.url || "/placeholder.svg"}
            alt={album.name}
            className="w-48 h-48 rounded-lg object-cover shadow-2xl"
          />
        )}
        <div className="flex-1">
          <p className="text-sm text-gray-400 mb-2">Album</p>
          <h1 className="text-4xl md:text-6xl font-bold mb-4">{album.name}</h1>
          <div className="flex items-center space-x-2 text-gray-400">
            <span>{album.artists?.primary?.[0]?.name}</span>
            {album.year && (
              <>
                <span>•</span>
                <span>{album.year}</span>
              </>
            )}
            {album.songCount && (
              <>
                <span>•</span>
                <span>{album.songCount} songs</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Play Button */}
      <div className="mb-8">
        <button
          onClick={handlePlayAlbum}
          className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-medium transition-colors"
        >
          <PlayIcon className="w-5 h-5" />
          <span>Play Album</span>
        </button>
      </div>

      {/* Songs List */}
      {album.songs && album.songs.length > 0 && (
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-700">
            <h2 className="text-xl font-semibold">Songs</h2>
          </div>
          <div className="divide-y divide-gray-700">
            {album.songs.map((song, index) => {
              const isCurrentSong = currentSong?.id === song.id
              const songImage = song.image?.find((img) => img.quality === "50x50") || song.image?.[0]

              return (
                <div
                  key={song.id}
                  className={`
                    flex items-center space-x-4 px-6 py-4 hover:bg-gray-700 cursor-pointer transition-colors
                    ${isCurrentSong ? "bg-gray-700" : ""}
                  `}
                  onClick={() => handlePlaySong(song, index)}
                >
                  <div className="w-8 text-center">
                    {isCurrentSong && isPlaying ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 flex items-center justify-center">
                          <div className="flex space-x-1">
                            <div className="w-1 h-3 bg-green-500 animate-pulse"></div>
                            <div
                              className="w-1 h-2 bg-green-500 animate-pulse"
                              style={{ animationDelay: "0.1s" }}
                            ></div>
                            <div
                              className="w-1 h-4 bg-green-500 animate-pulse"
                              style={{ animationDelay: "0.2s" }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">{index + 1}</span>
                    )}
                  </div>

                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {songImage && (
                      <img
                        src={songImage.url || "/placeholder.svg"}
                        alt={song.name}
                        className="w-10 h-10 rounded object-cover"
                      />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className={`font-medium truncate ${isCurrentSong ? "text-green-400" : "text-white"}`}>
                        {song.name}
                      </p>
                      <p className="text-sm text-gray-400 truncate">{song.artists?.primary?.[0]?.name}</p>
                    </div>
                  </div>

                  <div className="text-sm text-gray-400">{formatDuration(song.duration)}</div>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
