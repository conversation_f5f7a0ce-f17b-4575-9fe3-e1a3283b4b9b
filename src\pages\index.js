"use client"

import { useState, useEffect } from "react"
import { api } from "../utils/api"
import SongCard from "../components/music/SongCard"
import AlbumCard from "../components/music/AlbumCard"
import ArtistCard from "../components/music/ArtistCard"

export default function Home() {
  const [trendingSongs, setTrendingSongs] = useState([])
  const [trendingAlbums, setTrendingAlbums] = useState([])
  const [trendingArtists, setTrendingArtists] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchTrendingContent = async () => {
      try {
        // Fetch trending content using popular search terms
        const [songsRes, albumsRes, artistsRes] = await Promise.all([
          api.searchSongs("trending", 0, 12),
          api.searchAlbums("popular", 0, 8),
          api.searchArtists("top", 0, 8),
        ])

        if (songsRes.success) setTrendingSongs(songsRes.data.results)
        if (albumsRes.success) setTrendingAlbums(albumsRes.data.results)
        if (artistsRes.success) setTrendingArtists(artistsRes.data.results)
      } catch (error) {
        console.error("Error fetching trending content:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchTrendingContent()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-8">
      {/* Welcome Section */}
      <div className="text-center py-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg">
        <h1 className="text-4xl font-bold mb-4">Welcome to MusicStream</h1>
        <p className="text-xl text-gray-200">Discover and stream your favorite music</p>
      </div>

      {/* Trending Songs */}
      {trendingSongs.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold mb-6">Trending Songs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {trendingSongs.map((song) => (
              <SongCard key={song.id} song={song} songs={trendingSongs} />
            ))}
          </div>
        </section>
      )}

      {/* Popular Albums */}
      {trendingAlbums.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold mb-6">Popular Albums</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {trendingAlbums.map((album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </div>
        </section>
      )}

      {/* Top Artists */}
      {trendingArtists.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold mb-6">Top Artists</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {trendingArtists.map((artist) => (
              <ArtistCard key={artist.id} artist={artist} />
            ))}
          </div>
        </section>
      )}
    </div>
  )
}
