import Link from "next/link"
import { PlayIcon } from "@heroicons/react/24/solid"

export default function AlbumCard({ album }) {
  const albumImage = album.image?.find((img) => img.quality === "500x500") || album.image?.[0]

  return (
    <Link href={`/album/${album.id}`}>
      <div className="group bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-all duration-200 transform hover:scale-105 cursor-pointer">
        <div className="relative">
          {albumImage && (
            <img
              src={albumImage.url || "/placeholder.svg"}
              alt={album.name}
              className="w-full aspect-square object-cover rounded-md mb-4"
            />
          )}

          <div className="absolute bottom-2 right-2 p-3 bg-green-500 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-110">
            <PlayIcon className="w-5 h-5 text-white" />
          </div>
        </div>

        <h3 className="font-medium text-white truncate mb-1">{album.name}</h3>
        <p className="text-sm text-gray-400 truncate">{album.artists?.primary?.[0]?.name}</p>
        {album.year && <p className="text-xs text-gray-500 mt-1">{album.year}</p>}
      </div>
    </Link>
  )
}
