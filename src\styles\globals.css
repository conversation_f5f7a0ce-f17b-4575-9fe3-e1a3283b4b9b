@import "tailwindcss";

* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

html,
body {
    max-width: 100vw;
    overflow-x: hidden;
}

body {
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

a {
    color: inherit;
    text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Smooth animations */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, opacity 0.2s ease;
}

/* Loading animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Pulse animation for music bars */
@keyframes pulse {
    0%,
    100% {
        opacity: 0.4;
        transform: scaleY(0.4);
    }
    50% {
        opacity: 1;
        transform: scaleY(1);
    }
}

.animate-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Gradient backgrounds */
.gradient-bg {
    background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Music visualizer bars */
.music-bars {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
}

.music-bar {
    width: 3px;
    background: #10b981;
    border-radius: 2px;
    animation: musicBar 1.2s ease-in-out infinite;
}

.music-bar:nth-child(1) {
    animation-delay: 0s;
}
.music-bar:nth-child(2) {
    animation-delay: 0.1s;
}
.music-bar:nth-child(3) {
    animation-delay: 0.2s;
}
.music-bar:nth-child(4) {
    animation-delay: 0.3s;
}

@keyframes musicBar {
    0%,
    100% {
        height: 4px;
        opacity: 0.4;
    }
    50% {
        height: 16px;
        opacity: 1;
    }
}

/* Custom focus styles */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5);
}

/* Responsive design helpers */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
}

/* Dark theme variables */
:root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 17, 24, 39;
    --background-end-rgb: 17, 24, 39;
}
