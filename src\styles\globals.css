@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* CSS Variables for theming */
:root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 10, 10, 10;
    --background-end-rgb: 10, 10, 10;
    --accent-primary: #1db954;
    --accent-secondary: #1ed760;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-tertiary: #6b7280;
    --border-primary: #2a2a2a;
    --border-secondary: #3a3a3a;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --shadow-glow: 0 0 20px rgba(29, 185, 84, 0.3);
    --radius: 0.75rem;
}

/* Reset and base styles */
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

html {
    scroll-behavior: smooth;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}

html,
body {
    max-width: 100vw;
    overflow-x: hidden;
    font-family: "Inter", system-ui, -apple-system, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    color: var(--text-primary);
    background: radial-gradient(ellipse at top, rgba(29, 185, 84, 0.1) 0%, transparent 50%),
        linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
    min-height: 100vh;
    position: relative;
}

/* Background pattern overlay */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 25% 25%, rgba(29, 185, 84, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

a {
    color: inherit;
    text-decoration: none;
    transition: all 0.2s ease;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(42, 42, 42, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #1db954, #1ed760);
    border-radius: 4px;
    transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1ed760, #22c55e);
    box-shadow: 0 0 10px rgba(29, 185, 84, 0.5);
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: #1db954 rgba(42, 42, 42, 0.5);
}

/* Global smooth transitions */
* {
    transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced loading animations */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%,
    100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

/* Modern gradient backgrounds */
.gradient-bg {
    background: linear-gradient(135deg, #1db954 0%, #1ed760 25%, #8b5cf6 50%, #3b82f6 75%, #ec4899 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease-in-out infinite;
}

.gradient-text {
    background: linear-gradient(135deg, #1db954, #1ed760, #8b5cf6);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

/* Enhanced card hover effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-hover::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(29, 185, 84, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.card-hover:hover::before {
    left: 100%;
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 30px rgba(29, 185, 84, 0.2);
}

/* Glass morphism effects */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* Music visualizer components */
.music-bars {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    height: 20px;
}

.music-bar {
    width: 3px;
    background: linear-gradient(to top, #1db954, #1ed760);
    border-radius: 2px;
    animation: musicBar 1.2s ease-in-out infinite;
    transform-origin: bottom;
}

.music-bar:nth-child(1) {
    animation-delay: 0s;
}
.music-bar:nth-child(2) {
    animation-delay: 0.1s;
}
.music-bar:nth-child(3) {
    animation-delay: 0.2s;
}
.music-bar:nth-child(4) {
    animation-delay: 0.3s;
}
.music-bar:nth-child(5) {
    animation-delay: 0.4s;
}

@keyframes musicBar {
    0%,
    100% {
        height: 4px;
        opacity: 0.4;
    }
    50% {
        height: 20px;
        opacity: 1;
    }
}

/* Waveform visualization */
.waveform {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1px;
    height: 40px;
    overflow: hidden;
}

.waveform-bar {
    width: 2px;
    background: linear-gradient(to top, #1db954, #1ed760, #22c55e);
    border-radius: 1px;
    animation: waveform 1.5s ease-in-out infinite;
    transform-origin: center;
}

/* Staggered animation delays for waveform */
.waveform-bar:nth-child(odd) {
    animation-delay: 0s;
}
.waveform-bar:nth-child(even) {
    animation-delay: 0.2s;
}

@keyframes waveform {
    0%,
    100% {
        transform: scaleY(0.3);
        opacity: 0.6;
    }
    50% {
        transform: scaleY(1);
        opacity: 1;
    }
}

/* Enhanced focus styles */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(29, 185, 84, 0.5);
    border-color: var(--accent-primary);
}

.focus-ring:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Button styles */
.btn-primary {
    background: linear-gradient(135deg, #1db954, #1ed760);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(29, 185, 84, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

/* Loading states */
.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--accent-primary);
    animation: loadingDot 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
    animation-delay: -0.32s;
}
.loading-dot:nth-child(2) {
    animation-delay: -0.16s;
}
.loading-dot:nth-child(3) {
    animation-delay: 0s;
}

@keyframes loadingDot {
    0%,
    80%,
    100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Skeleton loading */
.skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s ease-in-out infinite;
    border-radius: 4px;
}

@keyframes skeleton {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive design helpers */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
    .mobile-only {
        display: block;
    }

    .card-hover:hover {
        transform: none;
        box-shadow: none;
    }

    .music-bars {
        height: 16px;
    }
    .music-bar {
        width: 2px;
    }

    @keyframes musicBar {
        0%,
        100% {
            height: 3px;
            opacity: 0.4;
        }
        50% {
            height: 16px;
            opacity: 1;
        }
    }
}

@media (min-width: 769px) {
    .mobile-only {
        display: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass,
    .glass-dark {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid var(--accent-primary);
    }

    .card-hover {
        border: 1px solid var(--border-primary);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .card-hover:hover {
        transform: none;
    }

    .music-bar,
    .waveform-bar {
        animation: none;
    }
}

/* Custom slider styles */
.slider {
    background: var(--border-primary);
    outline: none;
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1db954, #1ed760);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(29, 185, 84, 0.4);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(29, 185, 84, 0.6);
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1db954, #1ed760);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(29, 185, 84, 0.4);
    transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(29, 185, 84, 0.6);
}

.slider::-webkit-slider-track {
    background: var(--border-primary);
    border-radius: 4px;
}

.slider::-moz-range-track {
    background: var(--border-primary);
    border-radius: 4px;
    border: none;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .glass,
    .glass-dark {
        background: white !important;
        border: 1px solid black !important;
    }
}
