"use client"

import { useState, useCallback } from "react"
import { useLocalStorage } from "./useLocalStorage"

export const useRecentlyPlayed = () => {
  const [recentlyPlayed, setRecentlyPlayed] = useLocalStorage("recently-played", [])
  const [isLoading, setIsLoading] = useState(false)

  const addToRecentlyPlayed = useCallback(async (song) => {
    setIsLoading(true)
    try {
      const playedItem = {
        ...song,
        playedAt: new Date().toISOString(),
        playCount: 1
      }

      setRecentlyPlayed(prev => {
        // Remove existing entry if it exists
        const filtered = prev.filter(item => item.id !== song.id)
        
        // Add to beginning and limit to 50 items
        const updated = [playedItem, ...filtered].slice(0, 50)
        
        return updated
      })
    } catch (error) {
      console.error("Error adding to recently played:", error)
    } finally {
      setIsLoading(false)
    }
  }, [setRecentlyPlayed])

  const incrementPlayCount = useCallback(async (songId) => {
    setRecentlyPlayed(prev => 
      prev.map(item => 
        item.id === songId 
          ? { ...item, playCount: (item.playCount || 1) + 1, playedAt: new Date().toISOString() }
          : item
      )
    )
  }, [setRecentlyPlayed])

  const removeFromRecentlyPlayed = useCallback(async (songId) => {
    setIsLoading(true)
    try {
      setRecentlyPlayed(prev => prev.filter(item => item.id !== songId))
    } catch (error) {
      console.error("Error removing from recently played:", error)
    } finally {
      setIsLoading(false)
    }
  }, [setRecentlyPlayed])

  const clearRecentlyPlayed = useCallback(() => {
    setRecentlyPlayed([])
  }, [setRecentlyPlayed])

  const getRecentlyPlayedByPeriod = useCallback((days = 7) => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)
    
    return recentlyPlayed.filter(item => 
      new Date(item.playedAt) >= cutoffDate
    )
  }, [recentlyPlayed])

  const getMostPlayedSongs = useCallback((limit = 10) => {
    return [...recentlyPlayed]
      .sort((a, b) => (b.playCount || 1) - (a.playCount || 1))
      .slice(0, limit)
  }, [recentlyPlayed])

  const getRecentlyPlayedArtists = useCallback(() => {
    const artistMap = new Map()
    
    recentlyPlayed.forEach(song => {
      song.artists?.primary?.forEach(artist => {
        if (artistMap.has(artist.id)) {
          artistMap.set(artist.id, {
            ...artistMap.get(artist.id),
            playCount: artistMap.get(artist.id).playCount + 1
          })
        } else {
          artistMap.set(artist.id, {
            ...artist,
            playCount: 1
          })
        }
      })
    })
    
    return Array.from(artistMap.values())
      .sort((a, b) => b.playCount - a.playCount)
  }, [recentlyPlayed])

  return {
    recentlyPlayed,
    isLoading,
    addToRecentlyPlayed,
    incrementPlayCount,
    removeFromRecentlyPlayed,
    clearRecentlyPlayed,
    getRecentlyPlayedByPeriod,
    getMostPlayedSongs,
    getRecentlyPlayedArtists,
    totalPlayed: recentlyPlayed.length
  }
}
