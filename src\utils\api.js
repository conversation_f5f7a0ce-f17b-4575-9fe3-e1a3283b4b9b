const BASE_URL = "https://saavn.dev/api"

export const api = {
  // Search endpoints
  globalSearch: async (query) => {
    const response = await fetch(`${BASE_URL}/search?query=${encodeURIComponent(query)}`)
    return response.json()
  },

  searchSongs: async (query, page = 0, limit = 20) => {
    const response = await fetch(
      `${BASE_URL}/search/songs?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`,
    )
    return response.json()
  },

  searchAlbums: async (query, page = 0, limit = 20) => {
    const response = await fetch(
      `${BASE_URL}/search/albums?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`,
    )
    return response.json()
  },

  searchArtists: async (query, page = 0, limit = 20) => {
    const response = await fetch(
      `${BASE_URL}/search/artists?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`,
    )
    return response.json()
  },

  searchPlaylists: async (query, page = 0, limit = 20) => {
    const response = await fetch(
      `${BASE_URL}/search/playlists?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`,
    )
    return response.json()
  },

  // Song endpoints
  getSong: async (id) => {
    const response = await fetch(`${BASE_URL}/songs/${id}`)
    return response.json()
  },

  getSongSuggestions: async (id, limit = 10) => {
    const response = await fetch(`${BASE_URL}/songs/${id}/suggestions?limit=${limit}`)
    return response.json()
  },

  // Album endpoints
  getAlbum: async (id) => {
    const response = await fetch(`${BASE_URL}/albums?id=${id}`)
    return response.json()
  },

  // Artist endpoints
  getArtist: async (id) => {
    const response = await fetch(`${BASE_URL}/artists/${id}`)
    return response.json()
  },

  getArtistSongs: async (id, page = 0, sortBy = "popularity", sortOrder = "desc") => {
    const response = await fetch(`${BASE_URL}/artists/${id}/songs?page=${page}&sortBy=${sortBy}&sortOrder=${sortOrder}`)
    return response.json()
  },

  getArtistAlbums: async (id, page = 0, sortBy = "popularity", sortOrder = "desc") => {
    const response = await fetch(
      `${BASE_URL}/artists/${id}/albums?page=${page}&sortBy=${sortBy}&sortOrder=${sortOrder}`,
    )
    return response.json()
  },

  // Playlist endpoints
  getPlaylist: async (id, page = 0, limit = 50) => {
    const response = await fetch(`${BASE_URL}/playlists?id=${id}&page=${page}&limit=${limit}`)
    return response.json()
  },
}
