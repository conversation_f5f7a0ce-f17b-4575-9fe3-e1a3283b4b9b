"use client"

import { useState, useEffect, useCallback } from "react"
import { useLocalStorage } from "./useLocalStorage"

export const useFavorites = () => {
  const [favorites, setFavorites] = useLocalStorage("user-favorites", [])
  const [isLoading, setIsLoading] = useState(false)

  const isFavorite = useCallback((songId) => {
    return favorites.some(fav => fav.id === songId)
  }, [favorites])

  const addToFavorites = useCallback(async (song) => {
    if (isFavorite(song.id)) return
    
    setIsLoading(true)
    try {
      const favoriteItem = {
        ...song,
        addedAt: new Date().toISOString()
      }
      setFavorites(prev => [favoriteItem, ...prev])
    } catch (error) {
      console.error("Error adding to favorites:", error)
    } finally {
      setIsLoading(false)
    }
  }, [isFavorite, setFavorites])

  const removeFromFavorites = useCallback(async (songId) => {
    setIsLoading(true)
    try {
      setFavorites(prev => prev.filter(fav => fav.id !== songId))
    } catch (error) {
      console.error("Error removing from favorites:", error)
    } finally {
      setIsLoading(false)
    }
  }, [setFavorites])

  const toggleFavorite = useCallback(async (song) => {
    if (isFavorite(song.id)) {
      await removeFromFavorites(song.id)
    } else {
      await addToFavorites(song)
    }
  }, [isFavorite, addToFavorites, removeFromFavorites])

  const clearFavorites = useCallback(() => {
    setFavorites([])
  }, [setFavorites])

  const getFavoritesByArtist = useCallback((artistName) => {
    return favorites.filter(fav => 
      fav.artists?.primary?.some(artist => 
        artist.name.toLowerCase().includes(artistName.toLowerCase())
      )
    )
  }, [favorites])

  const getFavoritesByAlbum = useCallback((albumName) => {
    return favorites.filter(fav => 
      fav.album?.name?.toLowerCase().includes(albumName.toLowerCase())
    )
  }, [favorites])

  return {
    favorites,
    isLoading,
    isFavorite,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    clearFavorites,
    getFavoritesByArtist,
    getFavoritesByAlbum,
    favoritesCount: favorites.length
  }
}
