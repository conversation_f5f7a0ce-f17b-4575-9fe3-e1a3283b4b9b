"use client"

import { useState } from "react"
import { useRouter } from "next/router"
import { Bars3Icon, MagnifyingGlassIcon } from "@heroicons/react/24/outline"

export default function Header({ onMenuClick }) {
  const [searchQuery, setSearchQuery] = useState("")
  const router = useRouter()

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  return (
    <header className="bg-gray-800 border-b border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button onClick={onMenuClick} className="lg:hidden p-2 rounded-md hover:bg-gray-700 mr-2">
            <Bars3Icon className="w-6 h-6" />
          </button>

          <h2 className="text-lg font-semibold">
            {router.pathname === "/" && "Home"}
            {router.pathname === "/search" && "Search"}
            {router.pathname === "/my-playlists" && "My Playlists"}
            {router.pathname.startsWith("/artist/") && "Artist"}
            {router.pathname.startsWith("/album/") && "Album"}
            {router.pathname.startsWith("/playlist/") && "Playlist"}
          </h2>
        </div>

        <form onSubmit={handleSearch} className="flex-1 max-w-md mx-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search songs, artists, albums..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-full text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </form>

        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium">U</span>
          </div>
        </div>
      </div>
    </header>
  )
}
