"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/router";
import { Bars3Icon, MagnifyingGlassIcon, XMarkIcon, UserCircleIcon } from "@heroicons/react/24/outline";
import Button from "../ui/Button";
import { LoadingSpinner } from "../ui/LoadingSpinner";

export default function Header({ onMenuClick }) {
    const [searchQuery, setSearchQuery] = useState("");
    const [isSearchFocused, setIsSearchFocused] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const searchInputRef = useRef(null);
    const router = useRouter();

    // Keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e) => {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === "k") {
                e.preventDefault();
                searchInputRef.current?.focus();
            }
            // Escape to clear search
            if (e.key === "Escape" && isSearchFocused) {
                setSearchQuery("");
                searchInputRef.current?.blur();
            }
        };

        document.addEventListener("keydown", handleKeyDown);
        return () => document.removeEventListener("keydown", handleKeyDown);
    }, [isSearchFocused]);

    const handleSearch = async (e) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            setIsSearching(true);
            try {
                await router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
            } finally {
                setIsSearching(false);
            }
        }
    };

    const clearSearch = () => {
        setSearchQuery("");
        searchInputRef.current?.focus();
    };

    const getPageTitle = () => {
        switch (router.pathname) {
            case "/":
                return "Home";
            case "/search":
                return "Search";
            case "/my-playlists":
                return "My Playlists";
            case "/favorites":
                return "Liked Songs";
            case "/recently-played":
                return "Recently Played";
            default:
                if (router.pathname.startsWith("/artist/")) return "Artist";
                if (router.pathname.startsWith("/album/")) return "Album";
                if (router.pathname.startsWith("/playlist/")) return "Playlist";
                return "Music";
        }
    };

    return (
        <header className="glass-dark border-b border-border-secondary px-6 py-4 sticky top-0 z-40 backdrop-blur-xl">
            <div className="flex items-center justify-between">
                {/* Left Section */}
                <div className="flex items-center space-x-4">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={onMenuClick}
                        className="lg:hidden text-text-secondary hover:text-text-primary"
                    >
                        <Bars3Icon className="w-6 h-6" />
                    </Button>

                    <div className="flex items-center space-x-3">
                        <h1 className="text-2xl font-bold text-gradient">{getPageTitle()}</h1>
                        {router.query.q && <span className="text-sm text-text-tertiary">for "{router.query.q}"</span>}
                    </div>
                </div>

                {/* Search Section */}
                <form onSubmit={handleSearch} className="flex-1 max-w-2xl mx-8">
                    <div className="relative group">
                        <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-tertiary group-focus-within:text-accent-primary transition-colors" />

                        <input
                            ref={searchInputRef}
                            type="text"
                            placeholder="Search songs, artists, albums... (Ctrl+K)"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onFocus={() => setIsSearchFocused(true)}
                            onBlur={() => setIsSearchFocused(false)}
                            className="w-full pl-12 pr-12 py-3 bg-background-secondary border border-border-primary rounded-2xl text-text-primary placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary transition-all duration-200 hover:border-border-secondary"
                        />

                        {/* Clear button */}
                        {searchQuery && (
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={clearSearch}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 text-text-tertiary hover:text-text-primary"
                            >
                                <XMarkIcon className="w-4 h-4" />
                            </Button>
                        )}

                        {/* Search loading indicator */}
                        {isSearching && (
                            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                                <LoadingSpinner size="sm" variant="primary" />
                            </div>
                        )}

                        {/* Search focus ring */}
                        <div
                            className={`absolute inset-0 rounded-2xl transition-all duration-200 pointer-events-none ${
                                isSearchFocused ? "ring-2 ring-accent-primary/50 ring-offset-2 ring-offset-background-primary" : ""
                            }`}
                        />
                    </div>
                </form>

                {/* Right Section */}
                <div className="flex items-center space-x-3">
                    {/* User Profile */}
                    <Button variant="ghost" size="icon" className="text-text-secondary hover:text-text-primary">
                        <UserCircleIcon className="w-8 h-8" />
                    </Button>

                    {/* User Avatar */}
                    <div className="w-10 h-10 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center shadow-glow-sm hover:shadow-glow-md transition-all duration-200 cursor-pointer group">
                        <span className="text-sm font-bold text-white group-hover:scale-110 transition-transform">U</span>
                    </div>
                </div>
            </div>
        </header>
    );
}
