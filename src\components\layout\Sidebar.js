"use client"

import Link from "next/link"
import { useRouter } from "next/router"
import { HomeIcon, MagnifyingGlassIcon, MusicalNoteIcon, PlusIcon, XMarkIcon } from "@heroicons/react/24/outline"
import { usePlaylist } from "./Layout"

export default function Sidebar({ isOpen, onClose }) {
  const router = useRouter()
  const { playlists, createPlaylist } = usePlaylist()

  const navigation = [
    { name: "Home", href: "/", icon: HomeIcon },
    { name: "Search", href: "/search", icon: MagnifyingGlassIcon },
    { name: "My Playlists", href: "/my-playlists", icon: MusicalNoteIcon },
  ]

  const handleCreatePlaylist = () => {
    const name = prompt("Enter playlist name:")
    if (name) {
      createPlaylist(name)
    }
  }

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && <div className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" onClick={onClose} />}

      {/* Sidebar */}
      <div
        className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-gray-800 transform transition-transform duration-300 ease-in-out
        ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
      `}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h1 className="text-xl font-bold text-green-400">MusicStream</h1>
            <button onClick={onClose} className="lg:hidden p-1 rounded-md hover:bg-gray-700">
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = router.pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                    ${isActive ? "bg-green-600 text-white" : "text-gray-300 hover:bg-gray-700 hover:text-white"}
                  `}
                >
                  <item.icon className="w-5 h-5 mr-3" />
                  {item.name}
                </Link>
              )
            })}

            {/* Playlists Section */}
            <div className="pt-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">Playlists</h3>
                <button onClick={handleCreatePlaylist} className="p-1 rounded hover:bg-gray-700">
                  <PlusIcon className="w-4 h-4 text-gray-400" />
                </button>
              </div>

              <div className="space-y-1">
                {playlists.map((playlist) => (
                  <Link
                    key={playlist.id}
                    href={`/playlist/${playlist.id}`}
                    className="block px-3 py-2 text-sm text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors"
                  >
                    {playlist.name}
                  </Link>
                ))}
              </div>
            </div>
          </nav>
        </div>
      </div>
    </>
  )
}
