"use client";

import { useState, useRef, useEffect } from "react";
import {
    PlayIcon,
    PauseIcon,
    ForwardIcon,
    BackwardIcon,
    SpeakerWaveIcon,
    ChevronUpIcon,
    ChevronDownIcon,
    ArrowsRightLeftIcon,
    ArrowPathIcon,
    QueueListIcon,
    HeartIcon,
    EllipsisHorizontalIcon,
} from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";
import { usePlayer } from "../layout/Layout";
import { useFavorites } from "../../hooks/useFavorites";
import Button from "../ui/Button";
import Card from "../ui/Card";
import { MusicBars, LoadingSpinner } from "../ui/LoadingSpinner";

export default function AudioPlayer() {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showQueue, setShowQueue] = useState(false);
    const [showVolumeSlider, setShowVolumeSlider] = useState(false);
    const progressRef = useRef(null);
    const volumeRef = useRef(null);

    const {
        audioRef,
        currentSong,
        isPlaying,
        isLoading,
        error,
        currentTime,
        duration,
        volume,
        queue,
        currentIndex,
        repeatMode,
        shuffleMode,
        togglePlayPause,
        playNext,
        playPrevious,
        seek,
        changeVolume,
        toggleShuffle,
        toggleRepeat,
        hasNext,
        hasPrevious,
    } = usePlayer();

    const { isFavorite, toggleFavorite } = useFavorites();

    // Close expanded player on escape key
    useEffect(() => {
        const handleEscape = (e) => {
            if (e.key === "Escape" && isExpanded) {
                setIsExpanded(false);
            }
        };

        document.addEventListener("keydown", handleEscape);
        return () => document.removeEventListener("keydown", handleEscape);
    }, [isExpanded]);

    if (!currentSong) return null;

    const formatTime = (time) => {
        if (!time || isNaN(time)) return "0:00";
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    };

    const handleProgressClick = (e) => {
        if (!progressRef.current || !duration) return;
        const rect = progressRef.current.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        seek(percent * duration);
    };

    const handleVolumeChange = (e) => {
        const newVolume = parseFloat(e.target.value);
        changeVolume(newVolume);
    };

    const progressPercentage = duration ? (currentTime / duration) * 100 : 0;
    const songImage = currentSong.image?.find((img) => img.quality === "500x500") || currentSong.image?.[0];
    const artistName = currentSong.artists?.primary?.[0]?.name || "Unknown Artist";

    const getRepeatIcon = () => {
        switch (repeatMode) {
            case "one":
                return <ArrowPathIcon className="w-5 h-5 text-accent-primary" />;
            case "all":
                return <ArrowPathIcon className="w-5 h-5 text-accent-primary" />;
            default:
                return <ArrowPathIcon className="w-5 h-5 text-text-tertiary" />;
        }
    };

    return (
        <>
            <audio ref={audioRef} />

            {/* Mini Player */}
            <Card
                className={`
          fixed bottom-0 left-0 right-0 z-50 transition-all duration-500 ease-in-out
          ${isExpanded ? "h-screen rounded-none" : "h-24 rounded-t-2xl"}
          glass-dark border-t border-border-secondary shadow-player
        `}
                hover={false}
            >
                {!isExpanded ? (
                    <div className="flex items-center justify-between h-full px-6">
                        {/* Song Info */}
                        <div className="flex items-center space-x-4 flex-1 min-w-0">
                            <div className="relative group">
                                {songImage && (
                                    <img
                                        src={songImage.url || "/placeholder.svg"}
                                        alt={currentSong.name}
                                        className="w-14 h-14 rounded-xl object-cover shadow-lg group-hover:scale-105 transition-transform duration-200"
                                    />
                                )}
                                {isPlaying && (
                                    <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-xl">
                                        <MusicBars className="w-6 h-6" playing={isPlaying} />
                                    </div>
                                )}
                            </div>

                            <div className="min-w-0 flex-1">
                                <p className="text-sm font-semibold text-text-primary truncate hover:text-accent-primary transition-colors cursor-pointer">
                                    {currentSong.name}
                                </p>
                                <p className="text-xs text-text-secondary truncate">{artistName}</p>
                            </div>

                            {/* Favorite Button */}
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => toggleFavorite(currentSong)}
                                className="text-text-tertiary hover:text-accent-primary"
                            >
                                {isFavorite(currentSong.id) ? (
                                    <HeartIcon className="w-5 h-5 text-accent-primary" />
                                ) : (
                                    <HeartOutlineIcon className="w-5 h-5" />
                                )}
                            </Button>
                        </div>

                        {/* Center Controls */}
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={playPrevious}
                                disabled={!hasPrevious}
                                className="text-text-secondary hover:text-text-primary disabled:opacity-30"
                            >
                                <BackwardIcon className="w-5 h-5" />
                            </Button>

                            <Button
                                variant="primary"
                                size="icon"
                                onClick={togglePlayPause}
                                disabled={isLoading}
                                className="w-12 h-12 shadow-glow-sm hover:shadow-glow-md"
                            >
                                {isLoading ? (
                                    <LoadingSpinner size="sm" variant="white" />
                                ) : isPlaying ? (
                                    <PauseIcon className="w-6 h-6" />
                                ) : (
                                    <PlayIcon className="w-6 h-6 ml-0.5" />
                                )}
                            </Button>

                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={playNext}
                                disabled={!hasNext}
                                className="text-text-secondary hover:text-text-primary disabled:opacity-30"
                            >
                                <ForwardIcon className="w-5 h-5" />
                            </Button>
                        </div>

                        {/* Right Controls */}
                        <div className="flex items-center space-x-2 flex-1 justify-end">
                            {/* Progress Bar */}
                            <div className="hidden md:flex items-center space-x-2 flex-1 max-w-xs">
                                <span className="text-xs text-text-tertiary min-w-[35px]">{formatTime(currentTime)}</span>
                                <div
                                    ref={progressRef}
                                    className="flex-1 h-1 bg-background-hover rounded-full cursor-pointer group"
                                    onClick={handleProgressClick}
                                >
                                    <div
                                        className="h-full bg-gradient-to-r from-accent-primary to-accent-secondary rounded-full transition-all duration-100 group-hover:h-1.5"
                                        style={{ width: `${progressPercentage}%` }}
                                    />
                                </div>
                                <span className="text-xs text-text-tertiary min-w-[35px]">{formatTime(duration)}</span>
                            </div>

                            {/* Volume Control */}
                            <div className="hidden lg:flex items-center space-x-2">
                                <SpeakerWaveIcon className="w-4 h-4 text-text-tertiary" />
                                <input
                                    type="range"
                                    min="0"
                                    max="1"
                                    step="0.01"
                                    value={volume}
                                    onChange={handleVolumeChange}
                                    className="w-20 h-1 bg-background-hover rounded-full appearance-none cursor-pointer slider"
                                />
                            </div>

                            {/* Expand Button */}
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setIsExpanded(true)}
                                className="text-text-tertiary hover:text-text-primary"
                            >
                                <ChevronUpIcon className="w-5 h-5" />
                            </Button>
                        </div>
                    </div>
                ) : (
                    /* Expanded Player */
                    <div className="h-full flex flex-col animate-fade-in">
                        {/* Header */}
                        <div className="flex items-center justify-between p-6 border-b border-border-secondary">
                            <div className="flex items-center space-x-4">
                                <h3 className="text-xl font-bold text-gradient">Now Playing</h3>
                                {error && <span className="text-sm text-red-400 bg-red-400/10 px-3 py-1 rounded-full">{error}</span>}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => setShowQueue(!showQueue)}
                                    className={`${showQueue ? "text-accent-primary" : "text-text-tertiary"} hover:text-accent-primary`}
                                >
                                    <QueueListIcon className="w-5 h-5" />
                                </Button>

                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => setIsExpanded(false)}
                                    className="text-text-tertiary hover:text-text-primary"
                                >
                                    <ChevronDownIcon className="w-6 h-6" />
                                </Button>
                            </div>
                        </div>

                        {/* Main Content */}
                        <div className="flex-1 flex">
                            {/* Player Section */}
                            <div className="flex-1 flex items-center justify-center p-8">
                                <div className="max-w-lg w-full text-center">
                                    {/* Album Art */}
                                    <div className="relative mb-8 group">
                                        {songImage && (
                                            <img
                                                src={songImage.url || "/placeholder.svg"}
                                                alt={currentSong.name}
                                                className="w-80 h-80 mx-auto rounded-3xl shadow-2xl object-cover group-hover:scale-105 transition-transform duration-300"
                                            />
                                        )}

                                        {/* Overlay with music bars when playing */}
                                        {isPlaying && (
                                            <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                <MusicBars className="w-12 h-12" playing={isPlaying} />
                                            </div>
                                        )}
                                    </div>

                                    {/* Song Info */}
                                    <div className="mb-8">
                                        <h2 className="text-3xl font-bold mb-2 text-text-primary">{currentSong.name}</h2>
                                        <p className="text-lg text-text-secondary mb-4">{artistName}</p>

                                        {/* Action Buttons */}
                                        <div className="flex items-center justify-center space-x-4">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => toggleFavorite(currentSong)}
                                                className="text-text-tertiary hover:text-accent-primary"
                                            >
                                                {isFavorite(currentSong.id) ? (
                                                    <HeartIcon className="w-5 h-5 text-accent-primary mr-2" />
                                                ) : (
                                                    <HeartOutlineIcon className="w-5 h-5 mr-2" />
                                                )}
                                                {isFavorite(currentSong.id) ? "Liked" : "Like"}
                                            </Button>

                                            <Button variant="ghost" size="sm" className="text-text-tertiary hover:text-text-primary">
                                                <EllipsisHorizontalIcon className="w-5 h-5 mr-2" />
                                                More
                                            </Button>
                                        </div>
                                    </div>

                                    {/* Progress Bar */}
                                    <div className="mb-8">
                                        <div
                                            ref={progressRef}
                                            className="w-full h-2 bg-background-hover rounded-full cursor-pointer mb-4 group"
                                            onClick={handleProgressClick}
                                        >
                                            <div
                                                className="h-full bg-gradient-to-r from-accent-primary to-accent-secondary rounded-full transition-all duration-100 group-hover:h-3"
                                                style={{ width: `${progressPercentage}%` }}
                                            />
                                        </div>
                                        <div className="flex justify-between text-sm text-text-tertiary">
                                            <span>{formatTime(currentTime)}</span>
                                            <span>{formatTime(duration)}</span>
                                        </div>
                                    </div>

                                    {/* Main Controls */}
                                    <div className="flex items-center justify-center space-x-6 mb-8">
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={toggleShuffle}
                                            className={`${
                                                shuffleMode ? "text-accent-primary" : "text-text-tertiary"
                                            } hover:text-accent-primary`}
                                        >
                                            <ArrowsRightLeftIcon className="w-6 h-6" />
                                        </Button>

                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={playPrevious}
                                            disabled={!hasPrevious}
                                            className="text-text-secondary hover:text-text-primary disabled:opacity-30"
                                        >
                                            <BackwardIcon className="w-8 h-8" />
                                        </Button>

                                        <Button
                                            variant="primary"
                                            size="icon"
                                            onClick={togglePlayPause}
                                            disabled={isLoading}
                                            className="w-16 h-16 shadow-glow-lg hover:shadow-glow-lg hover:scale-105"
                                        >
                                            {isLoading ? (
                                                <LoadingSpinner size="lg" variant="white" />
                                            ) : isPlaying ? (
                                                <PauseIcon className="w-8 h-8" />
                                            ) : (
                                                <PlayIcon className="w-8 h-8 ml-1" />
                                            )}
                                        </Button>

                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={playNext}
                                            disabled={!hasNext}
                                            className="text-text-secondary hover:text-text-primary disabled:opacity-30"
                                        >
                                            <ForwardIcon className="w-8 h-8" />
                                        </Button>

                                        <Button variant="ghost" size="icon" onClick={toggleRepeat} className="hover:text-accent-primary">
                                            {getRepeatIcon()}
                                        </Button>
                                    </div>

                                    {/* Volume Control */}
                                    <div className="flex items-center justify-center space-x-4">
                                        <SpeakerWaveIcon className="w-5 h-5 text-text-tertiary" />
                                        <input
                                            type="range"
                                            min="0"
                                            max="1"
                                            step="0.01"
                                            value={volume}
                                            onChange={handleVolumeChange}
                                            className="w-40 h-2 bg-background-hover rounded-full appearance-none cursor-pointer slider"
                                        />
                                        <span className="text-sm text-text-tertiary min-w-[35px]">{Math.round(volume * 100)}%</span>
                                    </div>
                                </div>
                            </div>

                            {/* Queue Sidebar */}
                            {showQueue && (
                                <div className="w-80 border-l border-border-secondary p-6 animate-slide-left">
                                    <h4 className="text-lg font-semibold mb-4 text-text-primary">Up Next</h4>
                                    <div className="space-y-2 max-h-96 overflow-y-auto scrollbar-hide">
                                        {queue.slice(currentIndex + 1).map((song, index) => (
                                            <div
                                                key={song.id}
                                                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-background-hover transition-colors cursor-pointer"
                                            >
                                                <img
                                                    src={song.image?.[0]?.url || "/placeholder.svg"}
                                                    alt={song.name}
                                                    className="w-10 h-10 rounded-lg object-cover"
                                                />
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-text-primary truncate">{song.name}</p>
                                                    <p className="text-xs text-text-secondary truncate">
                                                        {song.artists?.primary?.[0]?.name}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </Card>
        </>
    );
}
