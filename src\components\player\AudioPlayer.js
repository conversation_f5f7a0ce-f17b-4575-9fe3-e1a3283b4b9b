"use client"

import { useState } from "react"
import {
  PlayIcon,
  PauseIcon,
  ForwardIcon,
  BackwardIcon,
  SpeakerWaveIcon,
  ChevronUpIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/solid"
import { usePlayer } from "../layout/Layout"

export default function AudioPlayer() {
  const [isExpanded, setIsExpanded] = useState(false)
  const {
    audioRef,
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    togglePlayPause,
    playNext,
    playPrevious,
    seek,
    changeVolume,
  } = usePlayer()

  if (!currentSong) return null

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  const handleProgressClick = (e) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const percent = (e.clientX - rect.left) / rect.width
    seek(percent * duration)
  }

  const handleVolumeChange = (e) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const percent = (e.clientX - rect.left) / rect.width
    changeVolume(Math.max(0, Math.min(1, percent)))
  }

  const songImage = currentSong.image?.find((img) => img.quality === "500x500") || currentSong.image?.[0]

  return (
    <>
      <audio ref={audioRef} />

      {/* Mini Player */}
      <div
        className={`
        fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 transition-all duration-300
        ${isExpanded ? "h-screen" : "h-20"}
      `}
      >
        {!isExpanded ? (
          <div className="flex items-center justify-between h-full px-4">
            {/* Song Info */}
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              {songImage && (
                <img
                  src={songImage.url || "/placeholder.svg"}
                  alt={currentSong.name}
                  className="w-12 h-12 rounded-md object-cover"
                />
              )}
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-white truncate">{currentSong.name}</p>
                <p className="text-xs text-gray-400 truncate">{currentSong.artists?.primary?.[0]?.name}</p>
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              <button onClick={playPrevious} className="p-2 hover:bg-gray-700 rounded-full transition-colors">
                <BackwardIcon className="w-5 h-5" />
              </button>

              <button
                onClick={togglePlayPause}
                className="p-3 bg-green-500 hover:bg-green-600 rounded-full transition-colors"
              >
                {isPlaying ? <PauseIcon className="w-6 h-6" /> : <PlayIcon className="w-6 h-6" />}
              </button>

              <button onClick={playNext} className="p-2 hover:bg-gray-700 rounded-full transition-colors">
                <ForwardIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Progress & Volume */}
            <div className="flex items-center space-x-4 flex-1 justify-end">
              <div className="hidden md:flex items-center space-x-2">
                <SpeakerWaveIcon className="w-4 h-4 text-gray-400" />
                <div className="w-20 h-1 bg-gray-600 rounded-full cursor-pointer" onClick={handleVolumeChange}>
                  <div className="h-full bg-green-500 rounded-full" style={{ width: `${volume * 100}%` }} />
                </div>
              </div>

              <button
                onClick={() => setIsExpanded(true)}
                className="p-2 hover:bg-gray-700 rounded-full transition-colors"
              >
                <ChevronUpIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        ) : (
          /* Expanded Player */
          <div className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-700">
              <h3 className="text-lg font-semibold">Now Playing</h3>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-2 hover:bg-gray-700 rounded-full transition-colors"
              >
                <ChevronDownIcon className="w-6 h-6" />
              </button>
            </div>

            {/* Main Content */}
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="max-w-md w-full text-center">
                {songImage && (
                  <img
                    src={songImage.url || "/placeholder.svg"}
                    alt={currentSong.name}
                    className="w-80 h-80 mx-auto rounded-lg shadow-2xl mb-8 object-cover"
                  />
                )}

                <h2 className="text-2xl font-bold mb-2">{currentSong.name}</h2>
                <p className="text-gray-400 mb-8">{currentSong.artists?.primary?.[0]?.name}</p>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div
                    className="w-full h-2 bg-gray-600 rounded-full cursor-pointer mb-2"
                    onClick={handleProgressClick}
                  >
                    <div
                      className="h-full bg-green-500 rounded-full transition-all duration-100"
                      style={{ width: `${(currentTime / duration) * 100 || 0}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-sm text-gray-400">
                    <span>{formatTime(currentTime)}</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                </div>

                {/* Controls */}
                <div className="flex items-center justify-center space-x-6 mb-8">
                  <button onClick={playPrevious} className="p-3 hover:bg-gray-700 rounded-full transition-colors">
                    <BackwardIcon className="w-8 h-8" />
                  </button>

                  <button
                    onClick={togglePlayPause}
                    className="p-4 bg-green-500 hover:bg-green-600 rounded-full transition-colors"
                  >
                    {isPlaying ? <PauseIcon className="w-10 h-10" /> : <PlayIcon className="w-10 h-10" />}
                  </button>

                  <button onClick={playNext} className="p-3 hover:bg-gray-700 rounded-full transition-colors">
                    <ForwardIcon className="w-8 h-8" />
                  </button>
                </div>

                {/* Volume Control */}
                <div className="flex items-center justify-center space-x-4">
                  <SpeakerWaveIcon className="w-5 h-5 text-gray-400" />
                  <div className="w-32 h-2 bg-gray-600 rounded-full cursor-pointer" onClick={handleVolumeChange}>
                    <div className="h-full bg-green-500 rounded-full" style={{ width: `${volume * 100}%` }} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
