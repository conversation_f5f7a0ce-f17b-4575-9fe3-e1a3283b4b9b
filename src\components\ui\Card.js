"use client"

import { forwardRef } from "react"

const Card = forwardRef(({ 
  children, 
  variant = "default", 
  hover = true,
  glass = false,
  className = "",
  ...props 
}, ref) => {
  const baseClasses = "rounded-xl transition-all duration-300"
  
  const variants = {
    default: "bg-background-card border border-border-primary shadow-card",
    glass: "glass border-border-secondary",
    glassDark: "glass-dark border-border-secondary",
    elevated: "bg-background-card border border-border-primary shadow-card-hover",
    gradient: "bg-gradient-to-br from-background-card to-background-tertiary border border-border-primary"
  }
  
  const hoverClasses = hover ? "card-hover cursor-pointer" : ""
  const glassClasses = glass ? "glass" : ""
  
  const classes = `${baseClasses} ${variants[variant]} ${hoverClasses} ${glassClasses} ${className}`
  
  return (
    <div
      ref={ref}
      className={classes}
      {...props}
    >
      {children}
    </div>
  )
})

Card.displayName = "Card"

export default Card
