"use client"

import { useState } from "react"
import SongCard from "../music/SongCard"
import AlbumCard from "../music/AlbumCard"
import ArtistCard from "../music/ArtistCard"

export default function SearchResults({ results }) {
  const [activeTab, setActiveTab] = useState("songs")

  if (!results) return null

  const tabs = [
    { id: "songs", label: "Songs", count: results.songs?.results?.length || 0 },
    { id: "albums", label: "Albums", count: results.albums?.results?.length || 0 },
    { id: "artists", label: "Artists", count: results.artists?.results?.length || 0 },
    { id: "playlists", label: "Playlists", count: results.playlists?.results?.length || 0 },
  ]

  return (
    <div className="space-y-6">
      {/* Top Results */}
      {results.topQuery?.results?.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold mb-4">Top Result</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {results.topQuery.results.slice(0, 4).map((item) => (
              <SongCard key={item.id} song={item} songs={results.topQuery.results} />
            ))}
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${
                  activeTab === tab.id
                    ? "border-green-500 text-green-400"
                    : "border-transparent text-gray-400 hover:text-gray-300"
                }
              `}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {activeTab === "songs" && results.songs?.results && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {results.songs.results.map((song) => (
              <SongCard key={song.id} song={song} songs={results.songs.results} />
            ))}
          </div>
        )}

        {activeTab === "albums" && results.albums?.results && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {results.albums.results.map((album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </div>
        )}

        {activeTab === "artists" && results.artists?.results && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {results.artists.results.map((artist) => (
              <ArtistCard key={artist.id} artist={artist} />
            ))}
          </div>
        )}

        {activeTab === "playlists" && results.playlists?.results && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {results.playlists.results.map((playlist) => (
              <div key={playlist.id} className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors">
                <h3 className="font-medium text-white truncate">{playlist.name}</h3>
                <p className="text-sm text-gray-400">Playlist</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
