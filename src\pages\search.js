"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import { api } from "../utils/api"
import SearchResults from "../components/search/SearchResults"

export default function Search() {
  const router = useRouter()
  const { q } = router.query
  const [results, setResults] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (q) {
      performSearch(q)
    }
  }, [q])

  const performSearch = async (query) => {
    setLoading(true)
    try {
      const response = await api.globalSearch(query)
      if (response.success) {
        setResults(response.data)
      }
    } catch (error) {
      console.error("Search error:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6">
      {q && (
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">Search Results</h1>
          <p className="text-gray-400">Results for "{q}"</p>
        </div>
      )}

      {loading ? (
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : results ? (
        <SearchResults results={results} />
      ) : q ? (
        <div className="text-center py-12">
          <p className="text-gray-400">No results found for "{q}"</p>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-400">Enter a search term to find music</p>
        </div>
      )}
    </div>
  )
}
