/** @type {import('tailwindcss').Config} */

export default {
    content: [
        "./pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./components/**/*.{js,ts,jsx,tsx,mdx}",
        "./app/**/*.{js,ts,jsx,tsx,mdx}",
        "./src/**/*.{js,ts,jsx,tsx,mdx}",
        "*.{js,ts,jsx,tsx,mdx}",
    ],
    darkMode: "class",
    theme: {
        extend: {
            colors: {
                // Modern dark theme colors
                background: {
                    primary: "#0a0a0a",
                    secondary: "#111111",
                    tertiary: "#1a1a1a",
                    card: "#1e1e1e",
                    hover: "#252525",
                },
                accent: {
                    primary: "#1db954", // Spotify green
                    secondary: "#1ed760",
                    tertiary: "#1fdf64",
                    purple: "#8b5cf6",
                    blue: "#3b82f6",
                    pink: "#ec4899",
                    orange: "#f97316",
                },
                text: {
                    primary: "#ffffff",
                    secondary: "#b3b3b3",
                    tertiary: "#6b7280",
                    muted: "#4b5563",
                },
                border: {
                    primary: "#2a2a2a",
                    secondary: "#3a3a3a",
                    accent: "#1db954",
                },
                // Enhanced primary colors
                primary: {
                    50: "#f0fdf4",
                    100: "#dcfce7",
                    200: "#bbf7d0",
                    300: "#86efac",
                    400: "#4ade80",
                    500: "#1db954",
                    600: "#16a34a",
                    700: "#15803d",
                    800: "#166534",
                    900: "#14532d",
                },
                // Enhanced gray scale
                gray: {
                    50: "#fafafa",
                    100: "#f4f4f5",
                    200: "#e4e4e7",
                    300: "#d4d4d8",
                    400: "#a1a1aa",
                    500: "#71717a",
                    600: "#52525b",
                    700: "#3f3f46",
                    800: "#27272a",
                    850: "#1f1f23",
                    900: "#18181b",
                    950: "#09090b",
                },
            },
            animation: {
                // Enhanced animations
                "fade-in": "fadeIn 0.5s ease-in-out",
                "fade-out": "fadeOut 0.5s ease-in-out",
                "slide-up": "slideUp 0.3s ease-out",
                "slide-down": "slideDown 0.3s ease-out",
                "slide-left": "slideLeft 0.3s ease-out",
                "slide-right": "slideRight 0.3s ease-out",
                "scale-in": "scaleIn 0.2s ease-out",
                "scale-out": "scaleOut 0.2s ease-out",
                "bounce-gentle": "bounceGentle 0.6s ease-out",
                "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
                "spin-slow": "spin 3s linear infinite",
                wiggle: "wiggle 1s ease-in-out infinite",
                glow: "glow 2s ease-in-out infinite alternate",
                float: "float 3s ease-in-out infinite",
                "music-bar": "musicBar 1.2s ease-in-out infinite",
                waveform: "waveform 1.5s ease-in-out infinite",
                "gradient-shift": "gradientShift 3s ease-in-out infinite",
            },
            keyframes: {
                fadeIn: {
                    "0%": { opacity: "0" },
                    "100%": { opacity: "1" },
                },
                fadeOut: {
                    "0%": { opacity: "1" },
                    "100%": { opacity: "0" },
                },
                slideUp: {
                    "0%": { transform: "translateY(100%)", opacity: "0" },
                    "100%": { transform: "translateY(0)", opacity: "1" },
                },
                slideDown: {
                    "0%": { transform: "translateY(-100%)", opacity: "0" },
                    "100%": { transform: "translateY(0)", opacity: "1" },
                },
                slideLeft: {
                    "0%": { transform: "translateX(100%)", opacity: "0" },
                    "100%": { transform: "translateX(0)", opacity: "1" },
                },
                slideRight: {
                    "0%": { transform: "translateX(-100%)", opacity: "0" },
                    "100%": { transform: "translateX(0)", opacity: "1" },
                },
                scaleIn: {
                    "0%": { transform: "scale(0.9)", opacity: "0" },
                    "100%": { transform: "scale(1)", opacity: "1" },
                },
                scaleOut: {
                    "0%": { transform: "scale(1)", opacity: "1" },
                    "100%": { transform: "scale(0.9)", opacity: "0" },
                },
                bounceGentle: {
                    "0%, 100%": { transform: "translateY(0)" },
                    "50%": { transform: "translateY(-10px)" },
                },
                wiggle: {
                    "0%, 100%": { transform: "rotate(-3deg)" },
                    "50%": { transform: "rotate(3deg)" },
                },
                glow: {
                    "0%": { boxShadow: "0 0 5px rgba(29, 185, 84, 0.5)" },
                    "100%": { boxShadow: "0 0 20px rgba(29, 185, 84, 0.8)" },
                },
                float: {
                    "0%, 100%": { transform: "translateY(0px)" },
                    "50%": { transform: "translateY(-10px)" },
                },
                musicBar: {
                    "0%, 100%": { height: "4px", opacity: "0.4" },
                    "50%": { height: "20px", opacity: "1" },
                },
                waveform: {
                    "0%, 100%": { transform: "scaleY(0.3)" },
                    "50%": { transform: "scaleY(1)" },
                },
                gradientShift: {
                    "0%, 100%": { backgroundPosition: "0% 50%" },
                    "50%": { backgroundPosition: "100% 50%" },
                },
            },
            backdropBlur: {
                xs: "2px",
                sm: "4px",
                md: "8px",
                lg: "12px",
                xl: "16px",
                "2xl": "24px",
                "3xl": "40px",
            },
            fontFamily: {
                sans: ["Inter", "system-ui", "sans-serif"],
                mono: ["JetBrains Mono", "monospace"],
            },
            borderRadius: {
                "4xl": "2rem",
                "5xl": "2.5rem",
                "6xl": "3rem",
            },
            boxShadow: {
                "glow-sm": "0 0 10px rgba(29, 185, 84, 0.3)",
                "glow-md": "0 0 20px rgba(29, 185, 84, 0.4)",
                "glow-lg": "0 0 30px rgba(29, 185, 84, 0.5)",
                "inner-glow": "inset 0 0 10px rgba(29, 185, 84, 0.2)",
                card: "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",
                "card-hover": "0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)",
                player: "0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)",
            },
            backgroundImage: {
                "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
                "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
                "gradient-mesh": "linear-gradient(135deg, #1db954 0%, #1ed760 25%, #8b5cf6 50%, #3b82f6 75%, #ec4899 100%)",
            },
            spacing: {
                18: "4.5rem",
                88: "22rem",
                128: "32rem",
            },
            zIndex: {
                60: "60",
                70: "70",
                80: "80",
                90: "90",
                100: "100",
            },
        },
    },
    plugins: [
        require("tailwindcss-animate"),
        function ({ addUtilities }) {
            const newUtilities = {
                ".glass": {
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                },
                ".glass-dark": {
                    background: "rgba(0, 0, 0, 0.3)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                },
                ".text-gradient": {
                    background: "linear-gradient(135deg, #1db954, #1ed760)",
                    "-webkit-background-clip": "text",
                    "-webkit-text-fill-color": "transparent",
                    "background-clip": "text",
                },
                ".scrollbar-hide": {
                    "-ms-overflow-style": "none",
                    "scrollbar-width": "none",
                    "&::-webkit-scrollbar": {
                        display: "none",
                    },
                },
            };
            addUtilities(newUtilities);
        },
    ],
};
