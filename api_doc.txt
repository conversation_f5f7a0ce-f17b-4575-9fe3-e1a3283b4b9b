v1.0.0
OAS 3.1.0
JioSaavn API

Download OpenAPI Document

Download OpenAPI Document
Introduction
JioSaavn API, accessible at saavn.dev, is an unofficial API that allows users to download high-quality songs from JioSaavn. It offers a fast, reliable, and easy-to-use API for developers.

Server
Server:
https://saavn.dev
Current environment

Client Libraries

Curl
Curl Shell
Search ​#Copy link
SearchOperations
get
/api/search
get
/api/search/songs
get
/api/search/albums
get
/api/search/artists
get
/api/search/playlists
Global search​#Copy link
Search for songs, albums, artists, and playlists based on the provided query string.

Query Parameters
query
Type:Search query
required
Example
Search query

Responses

200
Successful global search

application/json
Request Example for
get
/api/search
Selected HTTP client:Shell Curl

Curl
Copy content
curl 'https://saavn.dev/api/search?query=Imagine%20Dragons'

Test Request
(get /api/search)
Status:200
Copy content
{
  "success": true,
  "data": {
    "albums": {
      "results": [
        {
          "id": "string",
          "title": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "artist": "string",
          "url": "string",
          "type": "string",
          "description": "string",
          "year": "string",
          "language": "string",
          "songIds": "string"
        }
      ],
      "position": 1
    },
    "songs": {
      "results": [
        {
          "id": "string",
          "title": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "album": "string",
          "url": "string",
          "type": "string",
          "description": "string",
          "primaryArtists": "string",
          "singers": "string",
          "language": "string"
        }
      ],
      "position": 1
    },
    "artists": {
      "results": [
        {
          "id": "string",
          "title": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "type": "string",
          "description": "string",
          "position": 1
        }
      ],
      "position": 1
    },
    "playlists": {
      "results": [
        {
          "id": "string",
          "title": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "url": "string",
          "language": "string",
          "type": "string",
          "description": "string"
        }
      ],
      "position": 1
    },
    "topQuery": {
      "results": [
        {
          "id": "string",
          "title": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "album": "string",
          "url": "string",
          "type": "string",
          "description": "string",
          "primaryArtists": "string",
          "singers": "string",
          "language": "string"
        }
      ],
      "position": 1
    }
  }
}
Successful global search

Search for songs​#Copy link
Search for songs based on the provided query

Query Parameters
query
Type:Search query
required
Example
Search query for songs

page
Type:Page Number
default: 
"0"
Example
The page number of the search results to retrieve

limit
Type:Limit
default: 
"10"
Example
Number of search results per page

Responses

200
Successful response with song search results

application/json
Request Example for
get
/api/search/songs
Selected HTTP client:Shell Curl

Curl
Copy content
curl 'https://saavn.dev/api/search/songs?query=Believer'

Test Request
(get /api/search/songs)
Status:200
Copy content
{
  "success": true,
  "data": {
    "total": 1,
    "start": 1,
    "results": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ]
  }
}
Successful response with song search results

Search for albums​#Copy link
Search for albums based on the provided query

Query Parameters
query
Type:string
required
Example
Search query for albums

page
Type:integer
default: 
"0"
Example
The page number of the search results to retrieve

limit
Type:integer
default: 
"10"
Example
The number of search results per page

Responses

200
Successful response with album search results

application/json
Request Example for
get
/api/search/albums
Selected HTTP client:Shell Curl

Curl
Copy content
curl 'https://saavn.dev/api/search/albums?query=Evolve'

Test Request
(get /api/search/albums)
Status:200
Copy content
{
  "success": true,
  "data": {
    "total": 1,
    "start": 1,
    "results": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "year": null,
        "type": "string",
        "playCount": null,
        "language": "string",
        "explicitContent": true,
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "url": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ]
  }
}
Successful response with album search results

Search for artists​#Copy link
Search for artists based on the provided query

Query Parameters
query
Type:Search query
required
Example
Search query for artists

page
Type:Page Number
default: 
"0"
Example
The page number of the search results to retrieve

limit
Type:Limit
default: 
"10"
Example
Number of search results per page

Responses

200
Successful response with artist search results

application/json
Request Example for
get
/api/search/artists
Selected HTTP client:Shell Curl

Curl
Copy content
curl 'https://saavn.dev/api/search/artists?query=Adele'

Test Request
(get /api/search/artists)
Status:200
Copy content
{
  "success": true,
  "data": {
    "total": 1,
    "start": 1,
    "results": [
      {
        "id": "string",
        "name": "string",
        "role": "string",
        "type": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "url": "string"
      }
    ]
  }
}
Successful response with artist search results

Search for playlists​#Copy link
Search for playlists based on the provided query

Query Parameters
query
Type:Search query
required
Example
Search query for playlists

page
Type:Page Number
default: 
"0"
Example
The page number of the search results to retrieve

limit
Type:Limit
default: 
"10"
Example
Number of search results per page

Responses

200
Successful response with playlist search results

application/json
Request Example for
get
/api/search/playlists
Selected HTTP client:Shell Curl

Curl
Copy content
curl 'https://saavn.dev/api/search/playlists?query=Indie'

Test Request
(get /api/search/playlists)
Status:200
Copy content
{
  "success": true,
  "data": {
    "total": 1,
    "start": 1,
    "results": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "url": "string",
        "songCount": null,
        "language": "string",
        "explicitContent": true
      }
    ]
  }
}
Successful response with playlist search results

Songs ​#Copy link
SongsOperations
get
/api/songs
get
/api/songs/{id}
get
/api/songs/{id}/suggestions
Retrieve songs by ID or link​#Copy link
Retrieve songs by a comma-separated list of IDs or by a direct link to the song on JioSaavn.

Query Parameters
ids
Type:Song IDs
Example
Comma-separated list of song IDs

link
Type:Song Link
Example
A direct link to the song on JioSaavn

Responses

200
Successful response with song details

application/json
400
Bad request when query parameters are missing or invalid

404
Song not found with the given ID or link

Request Example for
get
/api/songs
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/songs

Test Request
(get /api/songs)
Status:200
Status:400
Status:404
Copy content
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "type": "string",
      "year": null,
      "releaseDate": null,
      "duration": null,
      "label": null,
      "explicitContent": true,
      "playCount": null,
      "language": "string",
      "hasLyrics": true,
      "lyricsId": null,
      "url": "string",
      "copyright": null,
      "album": {
        "id": null,
        "name": null,
        "url": null
      },
      "artists": {
        "primary": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ],
        "featured": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ],
        "all": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ]
      },
      "image": [
        {
          "quality": "string",
          "url": "string"
        }
      ],
      "downloadUrl": [
        {
          "quality": "string",
          "url": "string"
        }
      ]
    }
  ]
}
Successful response with song details

Retrieve song by ID​#Copy link
Retrieve a song by its ID. Optionally, include lyrics in the response.

Path Parameters
id
Type:Song ID
required
Example
ID of the song to retrieve

Responses

200
Successful response with song details

application/json
400
Bad request when query parameters are missing or invalid

404
Song not found for the given ID

Request Example for
get
/api/songs/{id}
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/songs/3IoDK8qI

Test Request
(get /api/songs/{id})
Status:200
Status:400
Status:404
Copy content
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "type": "string",
      "year": null,
      "releaseDate": null,
      "duration": null,
      "label": null,
      "explicitContent": true,
      "playCount": null,
      "language": "string",
      "hasLyrics": true,
      "lyricsId": null,
      "url": "string",
      "copyright": null,
      "album": {
        "id": null,
        "name": null,
        "url": null
      },
      "artists": {
        "primary": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ],
        "featured": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ],
        "all": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ]
      },
      "image": [
        {
          "quality": "string",
          "url": "string"
        }
      ],
      "downloadUrl": [
        {
          "quality": "string",
          "url": "string"
        }
      ]
    }
  ]
}
Successful response with song details

Retrieve song suggestions​#Copy link
Retrieve song suggestions based on the given song ID. This can be used to get similar songs to the one provided for infinite playback.

Path Parameters
id
Type:string
required
Example
ID of the song to retrieve suggestions for

Query Parameters
limit
Type:Limit
default: 
"10"
Example
Limit the number of suggestions to retrieve

Responses

200
Successful response with song suggestions

application/json
Request Example for
get
/api/songs/{id}/suggestions
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/songs/yDeAS8Eh/suggestions

Test Request
(get /api/songs/{id}/suggestions)
Status:200
Copy content
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "type": "string",
      "year": null,
      "releaseDate": null,
      "duration": null,
      "label": null,
      "explicitContent": true,
      "playCount": null,
      "language": "string",
      "hasLyrics": true,
      "lyricsId": null,
      "url": "string",
      "copyright": null,
      "album": {
        "id": null,
        "name": null,
        "url": null
      },
      "artists": {
        "primary": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ],
        "featured": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ],
        "all": [
          {
            "id": "string",
            "name": "string",
            "role": "string",
            "type": "string",
            "image": [
              {
                "quality": "string",
                "url": "string"
              }
            ],
            "url": "string"
          }
        ]
      },
      "image": [
        {
          "quality": "string",
          "url": "string"
        }
      ],
      "downloadUrl": [
        {
          "quality": "string",
          "url": "string"
        }
      ]
    }
  ]
}
Successful response with song suggestions

Album ​#Copy link
AlbumOperations
get
/api/albums
Retrieve an album by ID or link​#Copy link
Retrieve an album by providing either an ID or a direct link to the album on JioSaavn.

Query Parameters
id
Type:Album ID
default: 
"23241654"
Example
The unique ID of the album

link
Type:Album Link
default: 
"https://www.jiosaavn.com/album/future-nostalgia/ITIyo-GDr7A_"
Example
A direct link to the album on JioSaavn

Responses

200
Successful response with album details

application/json
400
Bad request due to missing or invalid query parameters.

404
The album could not be found with the provided ID or link.

Request Example for
get
/api/albums
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/albums

Test Request
(get /api/albums)
Status:200
Status:400
Status:404
Copy content
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "description": "string",
    "year": null,
    "type": "string",
    "playCount": null,
    "language": "string",
    "explicitContent": true,
    "artists": {
      "primary": [
        {
          "id": "string",
          "name": "string",
          "role": "string",
          "type": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "url": "string"
        }
      ],
      "featured": [
        {
          "id": "string",
          "name": "string",
          "role": "string",
          "type": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "url": "string"
        }
      ],
      "all": [
        {
          "id": "string",
          "name": "string",
          "role": "string",
          "type": "string",
          "image": [
            {
              "quality": "string",
              "url": "string"
            }
          ],
          "url": "string"
        }
      ]
    },
    "songCount": null,
    "url": "string",
    "image": [
      {
        "quality": "string",
        "url": "string"
      }
    ],
    "songs": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ]
  }
}
Successful response with album details

Artists ​#Copy link
ArtistsOperations
get
/api/artists
get
/api/artists/{id}
get
/api/artists/{id}/songs
get
/api/artists/{id}/albums
Retrieve artists by ID or link​#Copy link
Retrieve artists by ID or by a direct artist link.

Query Parameters
id
Type:Artist ID
Example
Artist ID

link
Type:Artist Link
Example
A direct link to the artist on JioSaavn

page
Type:Page number
Example
page number

songCount
Type:Song count
Example
Number of songs to fetch

albumCount
Type:Album count
Example
Number of albums to fetch

sortBy
Type:Sort by
Example
sort by

sortOrder
Type:Sort order
default: 
"desc"
Example
sort order

Responses

200
Successful response with artist details

application/json
Request Example for
get
/api/artists
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/artists

Test Request
(get /api/artists)
Status:200
Copy content
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "url": "string",
    "type": "string",
    "image": [
      {
        "quality": "string",
        "url": "string"
      }
    ],
    "followerCount": null,
    "fanCount": null,
    "isVerified": null,
    "dominantLanguage": null,
    "dominantType": null,
    "bio": [
      {
        "text": null,
        "title": null,
        "sequence": null
      }
    ],
    "dob": null,
    "fb": null,
    "twitter": null,
    "wiki": null,
    "availableLanguages": [
      "string"
    ],
    "isRadioPresent": null,
    "topSongs": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ],
    "topAlbums": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "year": null,
        "type": "string",
        "playCount": null,
        "language": "string",
        "explicitContent": true,
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "songCount": null,
        "url": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "songs": [
          {
            "id": "string",
            "name": "string",
            "type": "string",
            "year": null,
            "releaseDate": null,
            "duration": null,
            "label": null,
            "explicitContent": true,
            "playCount": null,
            "language": "string",
            "...": "[Additional Properties Truncated]"
          }
        ]
      }
    ],
    "singles": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ],
    "similarArtists": [
      {
        "id": "string",
        "name": "string",
        "url": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "languages": null,
        "wiki": "string",
        "dob": "string",
        "fb": "string",
        "twitter": "string",
        "isRadioPresent": true,
        "type": "string",
        "dominantType": "string",
        "aka": "string",
        "bio": null,
        "similarArtists": [
          {
            "id": "string",
            "name": "string"
          }
        ]
      }
    ]
  }
}
Successful response with artist details

Retrieve artist by ID​#Copy link
Retrieve artist by ID

Path Parameters
id
Type:Artist ID
required
Example
ID of the artist to retrieve

Query Parameters
page
Type:Page number
Example
The page number of the results to retrieve

songCount
Type:Song count
Example
The number of songs to retrieve for the artist

albumCount
Type:Album count
Example
The number of albums to retrieve for the artist

sortBy
Type:Sort by
enum
Example
The field to sort the results by

popularity
latest
alphabetical
sortOrder
Type:Sort order
enum
Example
The order to sort the results by

asc
desc
Responses

200
Successful response with artist details

application/json
404
Artist not found for the given ID

Request Example for
get
/api/artists/{id}
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/artists/1274170

Test Request
(get /api/artists/{id})
Status:200
Status:404
Copy content
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "url": "string",
    "type": "string",
    "image": [
      {
        "quality": "string",
        "url": "string"
      }
    ],
    "followerCount": null,
    "fanCount": null,
    "isVerified": null,
    "dominantLanguage": null,
    "dominantType": null,
    "bio": [
      {
        "text": null,
        "title": null,
        "sequence": null
      }
    ],
    "dob": null,
    "fb": null,
    "twitter": null,
    "wiki": null,
    "availableLanguages": [
      "string"
    ],
    "isRadioPresent": null,
    "topSongs": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ],
    "topAlbums": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "year": null,
        "type": "string",
        "playCount": null,
        "language": "string",
        "explicitContent": true,
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "songCount": null,
        "url": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "songs": [
          {
            "id": "string",
            "name": "string",
            "type": "string",
            "year": null,
            "releaseDate": null,
            "duration": null,
            "label": null,
            "explicitContent": true,
            "playCount": null,
            "language": "string",
            "...": "[Additional Properties Truncated]"
          }
        ]
      }
    ],
    "singles": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ],
    "similarArtists": [
      {
        "id": "string",
        "name": "string",
        "url": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "languages": null,
        "wiki": "string",
        "dob": "string",
        "fb": "string",
        "twitter": "string",
        "isRadioPresent": true,
        "type": "string",
        "dominantType": "string",
        "aka": "string",
        "bio": null,
        "similarArtists": [
          {
            "id": "string",
            "name": "string"
          }
        ]
      }
    ]
  }
}
Successful response with artist details

Retrieve artist's songs​#Copy link
Retrieve a list of songs for a given artist by their ID, with optional sorting and pagination.

Path Parameters
id
Type:string
default: 
"1274170"
required
Example
ID of the artist to retrieve the songs for

Query Parameters
page
Type:number
default: 
"0"
Example
The page number of the results to retrieve

sortBy
Type:string
enum
default: 
"popularity"
Example
The criterion to sort the songs by

popularity
latest
alphabetical
sortOrder
Type:string
enum
default: 
"desc"
Example
The order to sort the songs

asc
desc
Responses

200
Successful response with a list of songs for the artist

application/json
404
Artist not found for the given ID

Request Example for
get
/api/artists/{id}/songs
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/artists/1274170/songs

Test Request
(get /api/artists/{id}/songs)
Status:200
Status:404
Copy content
{
  "success": true,
  "data": {
    "total": 1,
    "songs": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ]
  }
}
Successful response with a list of songs for the artist

Retrieve artist's albums​#Copy link
Retrieve a list of albums for a given artist by their ID, with optional sorting and pagination.

Path Parameters
id
Type:string
default: 
"1274170"
required
Example
ID of the artist to retrieve the albums for

Query Parameters
page
Type:number
default: 
"0"
Example
The page number of the results to retrieve

sortBy
Type:string
enum
default: 
"popularity"
Example
The criterion to sort the albums by

popularity
latest
alphabetical
sortOrder
Type:string
enum
default: 
"desc"
Example
The order to sort the albums

asc
desc
Responses

200
Successful response with a list of albums for the artist

application/json
404
Artist not found for the given ID

Request Example for
get
/api/artists/{id}/albums
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/artists/1274170/albums

Test Request
(get /api/artists/{id}/albums)
Status:200
Status:404
Copy content
{
  "success": true,
  "data": {
    "total": 1,
    "albums": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "year": null,
        "type": "string",
        "playCount": null,
        "language": "string",
        "explicitContent": true,
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "songCount": null,
        "url": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "songs": [
          {
            "id": "string",
            "name": "string",
            "type": "string",
            "year": null,
            "releaseDate": null,
            "duration": null,
            "label": null,
            "explicitContent": true,
            "playCount": null,
            "language": "string",
            "...": "[Additional Properties Truncated]"
          }
        ]
      }
    ]
  }
}
Successful response with a list of albums for the artist

Playlist ​#Copy link
PlaylistOperations
get
/api/playlists
Retrieve a playlist by ID or link​#Copy link
Retrieve a playlist by providing either an ID or a direct link to the playlist on JioSaavn.

Query Parameters
id
Type:Playlist ID
default: 
"82914609"
Example
The unique ID of the playlist

link
Type:Playlist Link
default: 
"https://www.jiosaavn.com/featured/its-indie-english/AMoxtXyKHoU_"
Example
A direct link to the playlist on JioSaavn

page
Type:Page Number
default: 
"0"
Example
The page number of the songs to retrieve from the playlist

limit
Type:Limit
default: 
"10"
Example
Number of songs to retrieve per page

Responses

200
Successful response with playlist details

application/json
400
Bad request due to missing or invalid query parameters.

404
The playlist could not be found with the provided ID or link.

Request Example for
get
/api/playlists
Selected HTTP client:Shell Curl

Curl
Copy content
curl https://saavn.dev/api/playlists

Test Request
(get /api/playlists)
Status:200
Status:400
Status:404
Copy content
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "description": null,
    "year": null,
    "type": "string",
    "playCount": null,
    "language": "string",
    "explicitContent": true,
    "songCount": null,
    "url": "string",
    "image": [
      {
        "quality": "string",
        "url": "string"
      }
    ],
    "songs": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "year": null,
        "releaseDate": null,
        "duration": null,
        "label": null,
        "explicitContent": true,
        "playCount": null,
        "language": "string",
        "hasLyrics": true,
        "lyricsId": null,
        "url": "string",
        "copyright": null,
        "album": {
          "id": null,
          "name": null,
          "url": null
        },
        "artists": {
          "primary": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "featured": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ],
          "all": [
            {
              "id": "string",
              "name": "string",
              "role": "string",
              "type": "string",
              "image": [
                {
                  "quality": "string",
                  "url": "string"
                }
              ],
              "url": "string"
            }
          ]
        },
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "downloadUrl": [
          {
            "quality": "string",
            "url": "string"
          }
        ]
      }
    ],
    "artists": [
      {
        "id": "string",
        "name": "string",
        "role": "string",
        "type": "string",
        "image": [
          {
            "quality": "string",
            "url": "string"
          }
        ],
        "url": "string"
      }
    ]
  }
}
Successful response with playlist details